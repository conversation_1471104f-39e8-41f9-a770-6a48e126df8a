const fs = require('fs')
const path = require('path')

console.log('🔍 验证Node.js版本升级结果...')

const cloudfunctionsDir = path.join(__dirname, '../cloudfunctions')
const buildCloudfunctionsDir = path.join(__dirname, '../dist/build/mp-weixin/cloudfunctions')

// 期望的配置
const expectedConfigs = {
  'add': { runtime: 'Nodejs18.15', memorySize: 256, timeout: 30 },
  'getHistory': { runtime: 'Nodejs18.15', memorySize: 256, timeout: 30 },
  'submitFeedback': { runtime: 'Nodejs18.15', memorySize: 256, timeout: 30 },
  'checkText': { runtime: 'Nodejs18.15', memorySize: 512, timeout: 60 },
  'checkImage': { runtime: 'Nodejs18.15', memorySize: 512, timeout: 60 }
}

let allPassed = true

// 验证源目录配置
console.log('\n📁 验证源目录配置:')
Object.entries(expectedConfigs).forEach(([funcName, expected]) => {
  const configPath = path.join(cloudfunctionsDir, funcName, 'config.json')
  
  if (!fs.existsSync(configPath)) {
    console.error(`❌ ${funcName}: config.json 不存在`)
    allPassed = false
    return
  }
  
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
    
    // 检查关键配置
    const checks = [
      { key: 'runtime', expected: expected.runtime, actual: config.runtime },
      { key: 'memorySize', expected: expected.memorySize, actual: config.memorySize },
      { key: 'timeout', expected: expected.timeout, actual: config.timeout },
      { key: 'installDependency', expected: true, actual: config.installDependency }
    ]
    
    let funcPassed = true
    checks.forEach(({ key, expected, actual }) => {
      if (actual !== expected) {
        console.error(`   ❌ ${key}: 期望 ${expected}, 实际 ${actual}`)
        funcPassed = false
        allPassed = false
      }
    })
    
    if (funcPassed) {
      console.log(`✅ ${funcName}: 配置正确`)
      console.log(`   📦 ${config.runtime}, ${config.memorySize}MB, ${config.timeout}s`)
    }
    
  } catch (error) {
    console.error(`❌ ${funcName}: 配置文件解析失败`, error.message)
    allPassed = false
  }
})

// 验证构建目录配置
console.log('\n📦 验证构建目录配置:')
if (!fs.existsSync(buildCloudfunctionsDir)) {
  console.error('❌ 构建目录不存在，请先运行构建命令')
  allPassed = false
} else {
  Object.entries(expectedConfigs).forEach(([funcName, expected]) => {
    const configPath = path.join(buildCloudfunctionsDir, funcName, 'config.json')
    
    if (!fs.existsSync(configPath)) {
      console.error(`❌ ${funcName}: 构建目录中config.json不存在`)
      allPassed = false
      return
    }
    
    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
      
      if (config.runtime === expected.runtime && 
          config.memorySize === expected.memorySize && 
          config.timeout === expected.timeout) {
        console.log(`✅ ${funcName}: 构建配置正确`)
      } else {
        console.error(`❌ ${funcName}: 构建配置不匹配`)
        allPassed = false
      }
      
    } catch (error) {
      console.error(`❌ ${funcName}: 构建配置解析失败`, error.message)
      allPassed = false
    }
  })
}

// 验证package.json依赖
console.log('\n📦 验证AI云函数依赖:')
const aiFunctions = ['checkText', 'checkImage']
aiFunctions.forEach(funcName => {
  const packagePath = path.join(cloudfunctionsDir, funcName, 'package.json')
  
  if (!fs.existsSync(packagePath)) {
    console.error(`❌ ${funcName}: package.json 不存在`)
    allPassed = false
    return
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    const deps = packageJson.dependencies || {}
    
    const requiredDeps = {
      'wx-server-sdk': '~3.0.1',
      '@cloudbase/js-sdk': '^1.7.7',
      '@cloudbase/adapter-node': '^1.0.0'
    }
    
    let depsPassed = true
    Object.entries(requiredDeps).forEach(([dep, version]) => {
      if (!deps[dep]) {
        console.error(`   ❌ 缺少依赖: ${dep}`)
        depsPassed = false
        allPassed = false
      } else if (deps[dep] !== version) {
        console.warn(`   ⚠️ 版本不匹配: ${dep} (期望 ${version}, 实际 ${deps[dep]})`)
      }
    })
    
    if (depsPassed) {
      console.log(`✅ ${funcName}: 依赖配置正确`)
    }
    
  } catch (error) {
    console.error(`❌ ${funcName}: package.json解析失败`, error.message)
    allPassed = false
  }
})

// 验证cloudbaserc.json
console.log('\n☁️ 验证cloudbaserc.json:')
const cloudbasercPath = path.join(__dirname, '../cloudbaserc.json')
if (fs.existsSync(cloudbasercPath)) {
  try {
    const config = JSON.parse(fs.readFileSync(cloudbasercPath, 'utf8'))
    
    if (config.functions && Array.isArray(config.functions)) {
      let cloudbasercPassed = true
      
      config.functions.forEach(func => {
        const expected = expectedConfigs[func.name]
        if (expected) {
          if (func.memorySize !== expected.memorySize || func.timeout !== expected.timeout) {
            console.error(`❌ ${func.name}: cloudbaserc.json配置不匹配`)
            cloudbasercPassed = false
            allPassed = false
          }
        }
      })
      
      if (cloudbasercPassed) {
        console.log('✅ cloudbaserc.json: 配置正确')
      }
    }
  } catch (error) {
    console.error('❌ cloudbaserc.json: 解析失败', error.message)
    allPassed = false
  }
} else {
  console.error('❌ cloudbaserc.json: 文件不存在')
  allPassed = false
}

// 输出验证结果
console.log('\n' + '='.repeat(60))
if (allPassed) {
  console.log('🎉 Node.js版本升级验证通过!')
  console.log('✅ 所有云函数已升级到Node.js 18.15')
  console.log('✅ 内存和超时配置正确')
  console.log('✅ AI云函数依赖完整')
  console.log('✅ 构建配置同步正确')
  
  console.log('\n💡 下一步操作:')
  console.log('1. 在微信开发者工具中打开构建目录')
  console.log('2. 右键每个云函数选择"上传并部署：云端安装依赖"')
  console.log('3. 等待部署完成')
  console.log('4. 使用调试页面测试功能')
  
  console.log('\n🚀 Node.js 18 新特性:')
  console.log('- 更好的性能和内存管理')
  console.log('- 原生支持CloudBase AI SDK')
  console.log('- ES2022语言特性支持')
  console.log('- 改进的错误处理机制')
  
} else {
  console.log('❌ Node.js版本升级验证失败!')
  console.log('请修复上述错误后重新验证')
  
  console.log('\n🔧 修复建议:')
  console.log('1. 运行: npm run update:nodejs')
  console.log('2. 运行: npm run update:ai-sdk')
  console.log('3. 运行: npm run build:mp-weixin')
  console.log('4. 重新验证: npm run verify:nodejs')
  
  process.exit(1)
}

console.log(`\n📂 构建目录: ${path.join(__dirname, '../dist/build/mp-weixin')}`)
