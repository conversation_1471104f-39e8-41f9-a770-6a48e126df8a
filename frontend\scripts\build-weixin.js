const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始构建微信小程序...')

try {
  // 执行uni-app构建
  console.log('📦 执行uni-app构建...')
  execSync('uni build -p mp-weixin', { stdio: 'inherit' })
  
  // 构建后处理
  console.log('🔧 执行构建后处理...')
  
  const buildDir = path.join(__dirname, '../dist/build/mp-weixin')
  const sourceProjectConfig = path.join(__dirname, '../project.config.json')
  const targetProjectConfig = path.join(buildDir, 'project.config.json')
  
  // 确保构建目录存在
  if (!fs.existsSync(buildDir)) {
    throw new Error(`构建目录不存在: ${buildDir}`)
  }
  
  // 处理project.config.json
  let projectConfig = {}
  
  if (fs.existsSync(sourceProjectConfig)) {
    const configContent = fs.readFileSync(sourceProjectConfig, 'utf8')
    projectConfig = JSON.parse(configContent)
  }
  
  // 合并配置，确保包含必要字段
  const finalConfig = {
    ...projectConfig,
    appid: "wx042a805fca93fa5d",
    cloudfunctionRoot: "cloudfunctions/",
    miniprogramRoot: "./",
    setting: {
      urlCheck: false,
      es6: true,
      postcss: true,
      minified: true,
      enhance: true,
      packNpmManually: false,
      packNpmRelationList: [],
      minifyWXSS: true,
      showES6CompileOption: false,
      useCompilerPlugins: false,
      babelSetting: {
        ignore: [],
        disablePlugins: [],
        outputPath: ""
      },
      ...projectConfig.setting
    },
    compileType: "miniprogram",
    libVersion: "3.8.6",
    projectname: "news-verifier-ai",
    condition: {},
    editorSetting: {
      tabIndent: "insertSpaces",
      tabSize: 2
    }
  }
  
  // 写入配置文件
  fs.writeFileSync(targetProjectConfig, JSON.stringify(finalConfig, null, 2))
  console.log('✅ project.config.json 已更新')
  
  // 复制云函数目录
  const sourceCloudfunctions = path.join(__dirname, '../cloudfunctions')
  const targetCloudfunctions = path.join(buildDir, 'cloudfunctions')

  if (fs.existsSync(sourceCloudfunctions)) {
    // 删除目标目录（如果存在）
    if (fs.existsSync(targetCloudfunctions)) {
      fs.rmSync(targetCloudfunctions, { recursive: true, force: true })
    }

    // 复制云函数目录
    copyDirectory(sourceCloudfunctions, targetCloudfunctions)
    console.log('✅ 云函数目录已复制')

    // 列出复制的云函数
    const cloudFunctions = fs.readdirSync(targetCloudfunctions)
    console.log('📁 已复制的云函数:', cloudFunctions.join(', '))
  } else {
    console.warn('⚠️ 云函数目录不存在:', sourceCloudfunctions)
  }

  // 复制cloudbaserc.json文件
  const sourceCloudbaserc = path.join(__dirname, '../cloudbaserc.json')
  const targetCloudbaserc = path.join(buildDir, 'cloudbaserc.json')

  if (fs.existsSync(sourceCloudbaserc)) {
    fs.copyFileSync(sourceCloudbaserc, targetCloudbaserc)
    console.log('✅ cloudbaserc.json 已复制')
  } else {
    console.warn('⚠️ cloudbaserc.json 文件不存在:', sourceCloudbaserc)
  }

  // 复制其他重要配置文件
  const configFiles = [
    { src: 'package.json', desc: 'package.json' },
    { src: '.gitignore', desc: '.gitignore' }
  ]

  configFiles.forEach(({ src, desc }) => {
    const sourcePath = path.join(__dirname, '..', src)
    const targetPath = path.join(buildDir, src)

    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, targetPath)
      console.log(`✅ ${desc} 已复制`)
    }
  })
  
  // 验证构建结果
  console.log('🔍 验证构建结果...')

  const requiredFiles = [
    'app.js',
    'app.json',
    'app.wxss',
    'project.config.json',
    'cloudbaserc.json'
  ]
  
  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.join(buildDir, file))
  )
  
  if (missingFiles.length > 0) {
    console.warn('⚠️ 缺少文件:', missingFiles.join(', '))
  } else {
    console.log('✅ 所有必需文件都存在')
  }
  
  // 检查云函数
  if (fs.existsSync(targetCloudfunctions)) {
    const cloudFunctionCount = fs.readdirSync(targetCloudfunctions).length
    console.log(`✅ 云函数数量: ${cloudFunctionCount}`)
  }
  
  console.log('🎉 微信小程序构建完成!')
  console.log(`📂 构建目录: ${buildDir}`)
  console.log('💡 提示: 可以使用微信开发者工具打开构建目录进行预览和上传')
  
} catch (error) {
  console.error('❌ 构建失败:', error.message)
  process.exit(1)
}

// 递归复制目录
function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }
  
  const files = fs.readdirSync(src)
  
  files.forEach(file => {
    const srcPath = path.join(src, file)
    const destPath = path.join(dest, file)
    
    const stat = fs.statSync(srcPath)
    
    if (stat.isDirectory()) {
      copyDirectory(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  })
}
