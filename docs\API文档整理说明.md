# API接口文档整理说明

## 整理概述

基于对前端uni-app项目的深入分析，我已经为您整理了完整的API接口文档 (`docs/API接口文档.md`)，该文档详细描述了前端调用的所有API接口规范，供后端开发团队参考实现。

## 文档内容结构

### 1. 基础信息
- 协议规范 (HTTPS)
- 数据格式 (JSON/multipart)
- 通用响应格式
- 错误码说明

### 2. 核心API接口 (4个)

#### 2.1 文本内容核查接口
- **接口**: `POST /api/v1/check/text`
- **功能**: 接收用户输入的文本进行信息核查
- **前端调用位置**: `frontend/src/pages/index/index.vue` 的 `handleSubmit` 方法
- **请求参数**: 文本内容、用户ID、会话ID
- **响应数据**: 核查结果、可信度、来源链接、证据等

#### 2.2 图片内容核查接口
- **接口**: `POST /api/v1/check/image`
- **功能**: 接收图片文件，通过OCR提取文本后进行核查
- **前端调用位置**: `frontend/src/pages/index/index.vue` 的图片上传处理
- **请求方式**: multipart/form-data文件上传
- **响应数据**: 包含OCR结果和核查结果

#### 2.3 用户反馈提交接口
- **接口**: `POST /api/v1/feedback`
- **功能**: 接收用户对核查结果的评价反馈
- **前端调用位置**: `frontend/src/pages/result/result.vue` 的 `handleFeedback` 方法
- **请求参数**: 核查ID、评价类型、评论内容

#### 2.4 历史记录查询接口 (可选)
- **接口**: `GET /api/v1/history`
- **功能**: 获取用户云端历史记录 (如果实现云端存储)
- **前端调用位置**: 目前主要使用本地存储，云端接口为扩展功能

### 3. 前端实现分析

#### 3.1 当前实现状态
- ✅ 前端UI界面完整 (首页、结果页、历史页)
- ✅ 本地存储功能完整 (历史记录管理)
- ✅ 图片选择功能完整 (支持相册、相机、QQ会话选择)
- ⚠️ API调用部分为TODO状态，等待后端实现

#### 3.2 关键代码位置
- **主页面**: `frontend/src/pages/index/index.vue` (第176-205行)
- **结果页面**: `frontend/src/pages/result/result.vue` (第139-162行)
- **历史页面**: `frontend/src/pages/history/history.vue` (本地存储实现)

#### 3.3 平台特性支持
- **微信小程序**: 标准图片选择、云函数调用
- **QQ小程序**: 特殊的会话图片选择功能 (`qq.chooseMessageFile`)
- **跨平台**: 使用uni-app条件编译处理平台差异

### 4. 数据模型定义

文档中包含了完整的TypeScript接口定义：
- `CheckResult`: 核查结果数据模型
- `Source`: 信息来源数据模型  
- `OCRResult`: OCR识别结果模型
- `HistoryItem`: 历史记录数据模型

### 5. 前端调用示例

提供了完整的前端调用代码示例：
- uni.request 调用文本核查接口
- uni.uploadFile 调用图片核查接口
- 用户反馈提交示例
- 错误处理和超时处理

### 6. 实现指导

#### 6.1 网络请求封装
- 统一的API请求封装方法
- 错误处理和响应格式统一
- 请求头和基础URL配置

#### 6.2 本地存储管理
- 历史记录的增删改查
- 存储容量限制 (最多100条)
- 数据序列化和反序列化

#### 6.3 图片处理
- 跨平台图片选择统一封装
- QQ小程序特殊API处理
- 图片压缩和格式处理

### 7. 后端实现建议

#### 7.1 技术栈推荐
- Node.js + Express.js 或 Python + FastAPI
- Serverless部署 (腾讯云SCF/阿里云FC)
- MongoDB/PostgreSQL数据库
- 对象存储服务 (OSS/COS)

#### 7.2 核心处理流程
1. 请求接收与验证
2. 图片OCR处理 (如适用)
3. LLM文本理解与分析
4. 网络搜索与信息收集
5. 真伪判断与来源追溯
6. 结果格式化与返回

#### 7.3 性能优化
- 请求缓存机制
- 异步处理提高并发
- AI服务调用优化
- 分级处理策略

#### 7.4 安全考虑
- API鉴权和限流
- 输入内容安全过滤
- 敏感信息脱敏
- HTTPS加密传输

### 8. 测试用例

文档包含了完整的测试用例：
- 正常文本核查测试
- 空文本错误处理测试
- 图片OCR和核查测试
- 用户反馈提交测试

### 9. 云函数接口

当前项目包含一个测试云函数：
- **函数名**: `add`
- **功能**: 简单加法运算，用于测试云函数调用
- **位置**: `frontend/cloudfunctions/add/index.js`

## 后端开发建议

### 优先级排序
1. **高优先级**: 文本核查接口 - 核心功能
2. **高优先级**: 图片核查接口 - 核心功能  
3. **中优先级**: 用户反馈接口 - 用户体验优化
4. **低优先级**: 历史记录接口 - 扩展功能

### 开发步骤建议
1. 先实现基础的文本核查接口，返回模拟数据
2. 集成OCR服务，实现图片核查接口
3. 集成LLM服务，完善核查逻辑
4. 添加网络搜索功能
5. 实现用户反馈收集
6. 优化性能和错误处理

### 测试建议
1. 使用文档中提供的测试用例进行接口测试
2. 前端可以先使用mock数据进行界面调试
3. 逐步替换为真实API接口
4. 进行端到端测试验证

## 注意事项

1. **数据隐私**: 用户上传的图片和文本涉及隐私，需要严格按照隐私政策处理
2. **性能考虑**: 核查过程可能耗时较长，需要合理设置超时时间
3. **错误处理**: 需要完善的错误处理机制和用户友好的错误提示
4. **平台兼容**: 注意微信和QQ小程序的API差异
5. **扩展性**: 接口设计考虑了未来功能扩展的可能性

## 联系方式

如有任何关于API接口的疑问或需要进一步澄清的地方，请及时沟通。文档会根据开发过程中的实际需求进行更新和完善。
