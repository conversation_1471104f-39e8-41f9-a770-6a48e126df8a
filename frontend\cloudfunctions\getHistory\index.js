// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 获取数据库引用
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数getHistory被调用，事件参数:', event)
  console.log('上下文信息:', context)
  
  try {
    const wxContext = cloud.getWXContext()
    console.log('用户信息:', {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    })

    // 获取请求参数
    const { user_id, page = 1, limit = 20 } = event
    
    // 参数验证
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    
    if (pageNum < 1) {
      return {
        code: 400,
        message: '页码必须大于0',
        error: 'page parameter must be greater than 0',
        timestamp: new Date().toISOString()
      }
    }

    if (limitNum < 1 || limitNum > 100) {
      return {
        code: 400,
        message: '每页数量必须在1-100之间',
        error: 'limit parameter must be between 1 and 100',
        timestamp: new Date().toISOString()
      }
    }

    // 确定查询的用户ID
    const queryUserId = user_id || wxContext.OPENID
    
    console.log('查询用户历史记录:', {
      user_id: queryUserId,
      page: pageNum,
      limit: limitNum
    })

    // 计算跳过的记录数
    const skip = (pageNum - 1) * limitNum

    try {
      // 查询用户的历史记录总数
      const countResult = await db.collection('check_history')
        .where({
          user_id: queryUserId
        })
        .count()
      
      const total = countResult.total

      // 查询分页数据
      const historyResult = await db.collection('check_history')
        .where({
          user_id: queryUserId
        })
        .orderBy('check_time', 'desc') // 按时间倒序
        .skip(skip)
        .limit(limitNum)
        .get()

      // 处理查询结果
      const records = historyResult.data.map(record => ({
        check_id: record.check_id,
        content: record.content || record.summary || '核查内容',
        status: record.status,
        check_time: record.check_time,
        result_summary: record.summary || record.result_summary
      }))

      console.log(`查询完成，共找到 ${total} 条记录，当前页 ${records.length} 条`)

      return {
        code: 200,
        message: 'success',
        data: {
          total: total,
          page: pageNum,
          limit: limitNum,
          total_pages: Math.ceil(total / limitNum),
          records: records
        },
        timestamp: new Date().toISOString()
      }
      
    } catch (dbError) {
      console.error('数据库查询失败:', dbError)
      
      // 如果数据库查询失败，返回空结果
      return {
        code: 200,
        message: 'success',
        data: {
          total: 0,
          page: pageNum,
          limit: limitNum,
          total_pages: 0,
          records: []
        },
        timestamp: new Date().toISOString(),
        warning: '历史记录查询异常'
      }
    }
    
  } catch (error) {
    console.error('历史记录查询失败:', error)
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}
