const fs = require('fs')
const path = require('path')

console.log('🔄 更新云函数AI SDK依赖...')

const cloudfunctionsDir = path.join(__dirname, '../cloudfunctions')

// 需要更新AI SDK的云函数列表
const aiFunctions = ['checkText', 'checkImage']

// 检查云函数目录是否存在
if (!fs.existsSync(cloudfunctionsDir)) {
  console.error('❌ 云函数目录不存在:', cloudfunctionsDir)
  process.exit(1)
}

// 获取所有云函数
const allFunctions = fs.readdirSync(cloudfunctionsDir).filter(item => {
  const itemPath = path.join(cloudfunctionsDir, item)
  return fs.statSync(itemPath).isDirectory()
})

console.log('📁 发现云函数:', allFunctions.join(', '))

// 更新每个AI云函数的依赖
aiFunctions.forEach(funcName => {
  if (!allFunctions.includes(funcName)) {
    console.warn(`⚠️ 云函数 ${funcName} 不存在，跳过`)
    return
  }
  
  const funcDir = path.join(cloudfunctionsDir, funcName)
  const packageJsonPath = path.join(funcDir, 'package.json')
  
  if (!fs.existsSync(packageJsonPath)) {
    console.warn(`⚠️ ${funcName}/package.json 不存在，跳过`)
    return
  }
  
  try {
    // 读取现有的package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    
    // 确保有dependencies字段
    if (!packageJson.dependencies) {
      packageJson.dependencies = {}
    }
    
    // 添加CloudBase AI SDK依赖
    const requiredDeps = {
      'wx-server-sdk': '~3.0.1',
      '@cloudbase/js-sdk': '^1.7.7',
      '@cloudbase/adapter-node': '^1.0.0'
    }
    
    let updated = false
    Object.entries(requiredDeps).forEach(([dep, version]) => {
      if (!packageJson.dependencies[dep] || packageJson.dependencies[dep] !== version) {
        packageJson.dependencies[dep] = version
        updated = true
        console.log(`   📦 ${funcName}: 添加/更新依赖 ${dep}@${version}`)
      }
    })
    
    if (updated) {
      // 写回package.json
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
      console.log(`✅ ${funcName}: package.json 已更新`)
    } else {
      console.log(`✅ ${funcName}: 依赖已是最新`)
    }
    
  } catch (error) {
    console.error(`❌ ${funcName}: 更新失败`, error.message)
  }
})

// 检查其他云函数是否需要基础依赖
const otherFunctions = allFunctions.filter(func => !aiFunctions.includes(func))

otherFunctions.forEach(funcName => {
  const funcDir = path.join(cloudfunctionsDir, funcName)
  const packageJsonPath = path.join(funcDir, 'package.json')
  
  if (!fs.existsSync(packageJsonPath)) {
    console.warn(`⚠️ ${funcName}/package.json 不存在，跳过`)
    return
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    
    if (!packageJson.dependencies) {
      packageJson.dependencies = {}
    }
    
    // 确保有基础的wx-server-sdk依赖
    if (!packageJson.dependencies['wx-server-sdk']) {
      packageJson.dependencies['wx-server-sdk'] = '~3.0.1'
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
      console.log(`✅ ${funcName}: 添加基础依赖 wx-server-sdk`)
    }
    
  } catch (error) {
    console.error(`❌ ${funcName}: 检查失败`, error.message)
  }
})

console.log('\n📋 依赖更新总结:')
console.log('🔧 AI功能云函数 (使用CloudBase AI SDK):')
aiFunctions.forEach(func => {
  if (allFunctions.includes(func)) {
    console.log(`   ✅ ${func}`)
  } else {
    console.log(`   ❌ ${func} (不存在)`)
  }
})

console.log('🔧 其他云函数 (使用基础SDK):')
otherFunctions.forEach(func => {
  console.log(`   ✅ ${func}`)
})

console.log('\n💡 下一步操作:')
console.log('1. 运行: npm run install:cloud-deps')
console.log('2. 在微信开发者工具中重新部署云函数')
console.log('3. 选择"上传并部署：云端安装依赖"')
console.log('4. 测试AI功能是否正常工作')

console.log('\n📖 CloudBase AI SDK 特性:')
console.log('- 支持DeepSeek等多种AI模型')
console.log('- 统一的API接口')
console.log('- 流式响应支持')
console.log('- 自动错误处理')
console.log('- 更好的性能和稳定性')

console.log('\n🎉 AI SDK依赖更新完成!')
