const fs = require('fs')
const path = require('path')

// 构建后处理脚本
function postBuild() {
  console.log('开始执行构建后处理...')
  
  const buildDir = path.join(__dirname, '../dist/build/mp-weixin')
  const sourceProjectConfig = path.join(__dirname, '../project.config.json')
  const targetProjectConfig = path.join(buildDir, 'project.config.json')
  
  try {
    // 确保构建目录存在
    if (!fs.existsSync(buildDir)) {
      console.error('构建目录不存在:', buildDir)
      return
    }
    
    // 读取源配置文件
    if (fs.existsSync(sourceProjectConfig)) {
      const configContent = fs.readFileSync(sourceProjectConfig, 'utf8')
      const config = JSON.parse(configContent)
      
      // 确保包含必要的配置
      const requiredConfig = {
        ...config,
        appid: "wx042a805fca93fa5d",
        cloudfunctionRoot: "cloudfunctions/",
        miniprogramRoot: "./",
        setting: {
          ...config.setting,
          es6: true,
          postcss: true,
          minified: true,
          enhance: true,
          urlCheck: false
        }
      }
      
      // 写入目标配置文件
      fs.writeFileSync(targetProjectConfig, JSON.stringify(requiredConfig, null, 2))
      console.log('✅ project.config.json 已更新')
    } else {
      console.warn('⚠️ 源配置文件不存在:', sourceProjectConfig)
    }
    
    // 复制云函数目录
    const sourceCloudfunctions = path.join(__dirname, '../cloudfunctions')
    const targetCloudfunctions = path.join(buildDir, 'cloudfunctions')

    if (fs.existsSync(sourceCloudfunctions)) {
      // 递归复制云函数目录
      copyDirectory(sourceCloudfunctions, targetCloudfunctions)
      console.log('✅ 云函数目录已复制')
    } else {
      console.warn('⚠️ 云函数目录不存在:', sourceCloudfunctions)
    }

    // 复制cloudbaserc.json文件
    const sourceCloudbaserc = path.join(__dirname, '../cloudbaserc.json')
    const targetCloudbaserc = path.join(buildDir, 'cloudbaserc.json')

    if (fs.existsSync(sourceCloudbaserc)) {
      fs.copyFileSync(sourceCloudbaserc, targetCloudbaserc)
      console.log('✅ cloudbaserc.json 已复制')
    } else {
      console.warn('⚠️ cloudbaserc.json 文件不存在:', sourceCloudbaserc)
    }
    
    console.log('✅ 构建后处理完成')
    
  } catch (error) {
    console.error('❌ 构建后处理失败:', error)
  }
}

// 递归复制目录
function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }
  
  const files = fs.readdirSync(src)
  
  files.forEach(file => {
    const srcPath = path.join(src, file)
    const destPath = path.join(dest, file)
    
    const stat = fs.statSync(srcPath)
    
    if (stat.isDirectory()) {
      copyDirectory(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  })
}

// 执行构建后处理
postBuild()
