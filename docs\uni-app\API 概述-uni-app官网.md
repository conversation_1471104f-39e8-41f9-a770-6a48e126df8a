`uni-app`的 js API 由标准 ECMAScript 的 js API 和 uni 扩展 API 这两部分组成。

标准 ECMAScript 的 js 仅是最基础的 js。浏览器基于它扩展了 window、document、navigator 等对象。小程序也基于标准 js 扩展了各种 wx.xx、my.xx、swan.xx 的 API。node 也扩展了 fs 等模块。

uni-app 基于 ECMAScript 扩展了 uni 对象，并且 API 命名与小程序保持兼容。

## [#](https://zh.uniapp.dcloud.io/api/#%E6%A0%87%E5%87%86-js-%E5%92%8C%E6%B5%8F%E8%A7%88%E5%99%A8-js-%E7%9A%84%E5%8C%BA%E5%88%AB) 标准 js 和浏览器 js 的区别

`uni-app`的 js 代码，web 端运行于浏览器中。非 web 端（包含小程序和 App），Android 平台运行在 v8 引擎中，iOS 平台运行在 iOS 自带的 jscore 引擎中，都没有运行在浏览器或 webview 里。

非 web 端，虽然不支持 window、document、navigator 等浏览器的 js API，但也支持标准 ECMAScript。

请注意不要把浏览器里的 js 等价于标准 js。

所以 uni-app 的非web端，一样支持标准 js，支持 if、for 等语法，支持字符串、数字、时间、布尔值、数组、自定义对象等变量类型及各种处理方法。仅仅是不支持 window、document、navigator 等浏览器专用对象。

## [#](https://zh.uniapp.dcloud.io/api/#%E5%90%84%E7%AB%AF%E7%89%B9%E8%89%B2-api-%E8%B0%83%E7%94%A8) 各端特色 API 调用

除了 uni-app 框架内置的跨端 API，各端自己的特色 API 也可通过[条件编译](https://uniapp.dcloud.io/platform)自由使用。

各端特色 API 规范参考各端的开发文档。其中 App 端的 JS API 参考[html5plus.org](https://www.html5plus.org/doc/h5p.html)；uni-app 也支持通过扩展原生插件来丰富 App 端的开发能力，具体参考[插件开发文档](http://ask.dcloud.net.cn/article/35408)

各平台的 API 新增，不需要 uni-app 升级，开发者就可以直接使用。

各平台 API 独有的字段，如快手小程序 `ks.pay` 的 `payType`、`paymentChannel` 字段，开发者在调用 API 时正常传入即可，会透传至快手小程序的 API 上

## [#](https://zh.uniapp.dcloud.io/api/#%E8%A1%A5%E5%85%85%E8%AF%B4%E6%98%8E) 补充说明

-   uni.on 开头的 API 是监听某个事件发生的 API 接口，接受一个 CALLBACK 函数作为参数。当该事件触发时，会调用 CALLBACK 函数。
-   如未特殊约定，其他 API 接口都接受一个 OBJECT 作为参数。
-   OBJECT 中可以指定 success，fail，complete 来接收接口调用结果。
-   **平台差异说明**若无特殊说明，则表示所有平台均支持。
-   异步 API 会返回 `errMsg` 字段，同步 API 则不会。比如：`getSystemInfoSync` 在返回结果中不会有 `errMsg`。

## [#](https://zh.uniapp.dcloud.io/api/#api-promise-%E5%8C%96) API `Promise 化`

1.  具体 API `Promise 化` 的策略：
    
    -   异步的方法，如果不传入 success、fail、complete 等 callback 参数，将以 Promise 返回数据。例如：`uni.getImageInfo()`
        
    -   异步的方法，且有返回对象，如果希望获取返回对象，必须至少传入一项 success、fail、complete 等 callback 参数。例如：
        
2.  不进行 `Promise 化` 的 API：
    
    -   同步的方法（即以 sync 结束）。例如：`uni.getSystemInfoSync()`
    -   以 create 开头的方法。例如：`uni.createMapContext()`
    -   以 manager 结束的方法。例如：`uni.getBackgroundAudioManager()`

### [#](https://zh.uniapp.dcloud.io/api/#vue-2-%E5%92%8C-vue-3-%E7%9A%84-api-promise-%E5%8C%96) Vue 2 和 Vue 3 的 API `Promise 化`

> Vue 2 和 Vue 3 项目中 `API Promise 化` 返回格式不一致，以下为 `不同点` 和 `返回格式互相转换`

-   不同点
    
    -   Vue2 对部分 API 进行了 Promise 封装，返回数据的第一个参数是错误对象，第二个参数是返回数据。此时使用 `catch` 是拿不到报错信息的，因为内部对错误进行了拦截。
    -   Vue3 对部分 API 进行了 Promise 封装，调用成功会进入 `then 方法` 回调。调用失败会进入 `catch 方法` 回调
    
    **使用示例：**
    
-   返回格式互相转换
    

## [#](https://zh.uniapp.dcloud.io/api/#api-%E5%88%97%E8%A1%A8) API 列表

### [#](https://zh.uniapp.dcloud.io/api/#%E5%9F%BA%E7%A1%80) 基础

日志打印等。

| API | 说明 |
| --- | --- |
| [日志打印](https://zh.uniapp.dcloud.io/api/log) | 向控制台打印日志信息 |
| [定时器](https://zh.uniapp.dcloud.io/api/timer) | 在定时到期以后执行注册的回调函数 |
| [uni.base64ToArrayBuffer](https://zh.uniapp.dcloud.io/api/base64ToArrayBuffer) | 将 Base64 字符串转成 ArrayBuffer 对象 |
| [uni.arrayBufferToBase64](https://zh.uniapp.dcloud.io/api/arrayBufferToBase64) | 将 ArrayBuffer 对象转成 Base64 字符串 |
| [应用级事件](https://zh.uniapp.dcloud.io/api/application) | 监听应用事件 |
| [拦截器](https://zh.uniapp.dcloud.io/api/interceptor) | 拦截 Api 等调用并执行回调 |
| [全局 API](https://zh.uniapp.dcloud.io/api/global) | 可全局调用 Api |

### [#](https://zh.uniapp.dcloud.io/api/#%E7%BD%91%E7%BB%9C) 网络

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%8F%91%E8%B5%B7%E8%AF%B7%E6%B1%82) 发起请求

| API | 说明 |
| --- | --- |
| [uni.request](https://zh.uniapp.dcloud.io/api/request/request#request) | 发起网络请求 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E4%B8%8A%E4%BC%A0%E3%80%81%E4%B8%8B%E8%BD%BD) 上传、下载

| API | 说明 |
| --- | --- |
| [uni.uploadFile](https://zh.uniapp.dcloud.io/api/request/network-file#uploadfile) | 上传文件 |
| [uni.downloadFile](https://zh.uniapp.dcloud.io/api/request/network-file#downloadfile) | 下载文件 |

##### [#](https://zh.uniapp.dcloud.io/api/#websocket) WebSocket

| API | 说明 |
| --- | --- |
| [uni.connectSocket](https://zh.uniapp.dcloud.io/api/request/websocket#connectsocket) | 创建 WebSocket 连接 |
| [uni.onSocketOpen](https://zh.uniapp.dcloud.io/api/request/websocket#onsocketopen) | 监听 WebSocket 打开 |
| [uni.onSocketError](https://zh.uniapp.dcloud.io/api/request/websocket#onsocketerror) | 监听 WebSocket 错误 |
| [uni.sendSocketMessage](https://zh.uniapp.dcloud.io/api/request/websocket#sendsocketmessage) | 发送 WebSocket 消息 |
| [uni.onSocketMessage](https://zh.uniapp.dcloud.io/api/request/websocket#onsocketmessage) | 接受 WebSocket 消息 |
| [uni.closeSocket](https://zh.uniapp.dcloud.io/api/request/websocket#closesocket) | 关闭 WebSocket 连接 |
| [uni.onSocketClose](https://zh.uniapp.dcloud.io/api/request/websocket#onsocketclose) | 监听 WebSocket 关闭 |

##### [#](https://zh.uniapp.dcloud.io/api/#sockettask) SocketTask

| API | 说明 |
| --- | --- |
| [SocketTask.send](https://zh.uniapp.dcloud.io/api/request/socket-task#sockettasksend) | 通过 WebSocket 连接发送数据 |
| [SocketTask.close](https://zh.uniapp.dcloud.io/api/request/socket-task#sockettaskclose) | 关闭 WebSocket 连接 |
| [SocketTask.onOpen](https://zh.uniapp.dcloud.io/api/request/socket-task#sockettaskonopen) | 监听 WebSocket 连接打开事件 |
| [SocketTask.onClose](https://zh.uniapp.dcloud.io/api/request/socket-task#sockettaskonclose) | 监听 WebSocket 连接关闭事件 |
| [SocketTask.onError](https://zh.uniapp.dcloud.io/api/request/socket-task#sockettaskonerror) | 监听 WebSocket 错误事件 |
| [SocketTask.onMessage](https://zh.uniapp.dcloud.io/api/request/socket-task#sockettaskonmessage) | 监听 WebSocket 接受到服务器的消息事件 |

### [#](https://zh.uniapp.dcloud.io/api/#%E5%AA%92%E4%BD%93) 媒体

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%9B%BE%E7%89%87) 图片

| API | 说明 |
| --- | --- |
| [uni.chooseImage](https://zh.uniapp.dcloud.io/api/media/image#chooseimage) | 从相册选择图片，或者拍照 |
| [uni.previewImage](https://zh.uniapp.dcloud.io/api/media/image#unipreviewimageobject) | 预览图片 |
| [uni.closePreviewImage](https://zh.uniapp.dcloud.io/api/media/image#closepreviewimage) | 关闭预览图片 |
| [uni.getImageInfo](https://zh.uniapp.dcloud.io/api/media/image#getimageinfo) | 获取图片信息 |
| [uni.saveImageToPhotosAlbum](https://zh.uniapp.dcloud.io/api/media/image#saveimagetophotosalbum) | 保存图片到系统相册 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%96%87%E4%BB%B6) 文件

| API | 说明 |
| --- | --- |
| [uni.chooseFile](https://zh.uniapp.dcloud.io/api/media/file#chooseFile) | 从本地选择文件 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E5%BD%95%E9%9F%B3%E7%AE%A1%E7%90%86) 录音管理

| API | 说明 |
| --- | --- |
| [uni.getRecorderManager](https://zh.uniapp.dcloud.io/api/media/record-manager) | 录音管理 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E8%83%8C%E6%99%AF%E9%9F%B3%E9%A2%91%E6%92%AD%E6%94%BE%E7%AE%A1%E7%90%86) 背景音频播放管理

| API | 说明 |
| --- | --- |
| [uni.getBackgroundAudioManager](https://zh.uniapp.dcloud.io/api/media/background-audio-manager) | 背景音频播放管理 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E9%9F%B3%E9%A2%91%E7%BB%84%E4%BB%B6%E7%AE%A1%E7%90%86) 音频组件管理

| API | 说明 |
| --- | --- |
| [uni.createInnerAudioContext](https://zh.uniapp.dcloud.io/api/media/audio-context) | 音频组件管理 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E8%A7%86%E9%A2%91) 视频

| API | 说明 |
| --- | --- |
| [uni.chooseVideo](https://zh.uniapp.dcloud.io/api/media/video#choosevideo) | 从相册选择视频，或者拍摄 |
| [uni.chooseMedia](https://zh.uniapp.dcloud.io/api/media/video#choosemedia) | 拍摄或从手机相册中选择图片或视频。 |
| [uni.saveVideoToPhotosAlbum](https://zh.uniapp.dcloud.io/api/media/video#savevideotophotosalbum) | 保存视频到系统相册 |
| [uni.createVideoContext](https://zh.uniapp.dcloud.io/api/media/video-context#createvideocontext) | 视频组件管理 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E7%9B%B8%E6%9C%BA%E7%BB%84%E4%BB%B6%E7%AE%A1%E7%90%86) 相机组件管理

| API | 说明 |
| --- | --- |
| [uni.createCameraContext](https://zh.uniapp.dcloud.io/api/media/camera-context.html) | 相机组件管理 |

##### [#](https://zh.uniapp.dcloud.io/api/#%E7%9B%B4%E6%92%AD%E7%BB%84%E4%BB%B6%E7%AE%A1%E7%90%86) 直播组件管理

| API | 说明 |
| --- | --- |
| [uni.createLivePlayerContext](https://zh.uniapp.dcloud.io/api/media/live-player-context.html) | 直播组件管理 |

### [#](https://zh.uniapp.dcloud.io/api/#%E6%96%87%E4%BB%B6-2) 文件

| API | 说明 |
| --- | --- |
| [uni.saveFile](https://zh.uniapp.dcloud.io/api/file/file#savefile) | 保存文件 |
| [uni.getSavedFileList](https://zh.uniapp.dcloud.io/api/file/file#getsavedfilelist) | 获取已保存的文件列表 |
| [uni.getSavedFileInfo](https://zh.uniapp.dcloud.io/api/file/file#getsavedfileinfo) | 获取已保存的文件信息 |
| [uni.removeSavedFile](https://zh.uniapp.dcloud.io/api/file/file#removesavedfile) | 删除已保存的文件信息 |
| [uni.getFileInfo](https://zh.uniapp.dcloud.io/api/file/file#getfileinfo) | 获取文件信息 |
| [uni.openDocument](https://zh.uniapp.dcloud.io/api/file/file#opendocument) | 打开文件 |

### [#](https://zh.uniapp.dcloud.io/api/#%E6%95%B0%E6%8D%AE%E7%BC%93%E5%AD%98) 数据缓存

| API | 说明 |
| --- | --- |
| [uni.getStorage](https://zh.uniapp.dcloud.io/api/storage/storage#setstorage) | 获取本地数据缓存 |
| [uni.getStorageSync](https://zh.uniapp.dcloud.io/api/storage/storage#setstoragesync) | 获取本地数据缓存 |
| [uni.setStorage](https://zh.uniapp.dcloud.io/api/storage/storage#getstorage) | 设置本地数据缓存 |
| [uni.setStorageSync](https://zh.uniapp.dcloud.io/api/storage/storage#getstoragesync) | 设置本地数据缓存 |
| [uni.getStorageInfo](https://zh.uniapp.dcloud.io/api/storage/storage#getstorageinfo) | 获取本地缓存的相关信息 |
| [uni.getStorageInfoSync](https://zh.uniapp.dcloud.io/api/storage/storage#getstorageinfosync) | 获取本地缓存的相关信息 |
| [uni.removeStorage](https://zh.uniapp.dcloud.io/api/storage/storage#removestorage) | 删除本地缓存内容 |
| [uni.removeStorageSync](https://zh.uniapp.dcloud.io/api/storage/storage#removestoragesync) | 删除本地缓存内容 |
| [uni.clearStorage](https://zh.uniapp.dcloud.io/api/storage/storage#clearstorage) | 清理本地数据缓存 |
| [uni.clearStorageSync](https://zh.uniapp.dcloud.io/api/storage/storage#clearstoragesync) | 清理本地数据缓存 |

### [#](https://zh.uniapp.dcloud.io/api/#%E4%BD%8D%E7%BD%AE) 位置

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%8E%B7%E5%8F%96%E4%BD%8D%E7%BD%AE) 获取位置

| API | 说明 |
| --- | --- |
| [uni.getLocation](https://zh.uniapp.dcloud.io/api/location/location#getlocation) | 获取当前位置 |
| [uni.chooseLocation](https://zh.uniapp.dcloud.io/api/location/location#chooselocation) | 打开地图选择位置 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%9F%A5%E7%9C%8B%E4%BD%8D%E7%BD%AE) 查看位置

| API | 说明 |
| --- | --- |
| [uni.openLocation](https://zh.uniapp.dcloud.io/api/location/open-location#openlocation) | 打开内置地图 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%9C%B0%E5%9B%BE%E7%BB%84%E4%BB%B6%E6%8E%A7%E5%88%B6) 地图组件控制

| API | 说明 |
| --- | --- |
| [uni.createMapContext](https://zh.uniapp.dcloud.io/api/location/map#createmapcontext) | 地图组件控制 |

### [#](https://zh.uniapp.dcloud.io/api/#%E8%AE%BE%E5%A4%87) 设备

#### [#](https://zh.uniapp.dcloud.io/api/#%E7%B3%BB%E7%BB%9F%E4%BF%A1%E6%81%AF) 系统信息

| API | 说明 |
| --- | --- |
| [uni.getSystemInfo](https://zh.uniapp.dcloud.io/api/system/info#getsysteminfo) | 获取系统信息 |
| [uni.getSystemInfoSync](https://zh.uniapp.dcloud.io/api/system/info#getsysteminfosync) | 获取系统信息 |
| [uni.canIUse](https://zh.uniapp.dcloud.io/api/caniuse) | 判断应用的 API，回调，参数，组件等是否在当前版本可用 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%86%85%E5%AD%98) 内存

| API | 说明 |
| --- | --- |
| [uni.onMemoryWarning](https://zh.uniapp.dcloud.io/api/system/memory#wxonmemorywarning) | 监听内存不足告警事件 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E7%BD%91%E7%BB%9C%E7%8A%B6%E6%80%81) 网络状态

| API | 说明 |
| --- | --- |
| [uni.getNetworkType](https://zh.uniapp.dcloud.io/api/system/network#getnetworktype) | 获取网络类型 |
| [uni.onNetworkStatusChange](https://zh.uniapp.dcloud.io/api/system/network#onnetworkstatuschange) | 监听网络状态变化 |
| [uni.offNetworkStatusChange](https://zh.uniapp.dcloud.io/api/system/network#offnetworkstatuschange) | 取消监听网络状态变化 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%8A%A0%E9%80%9F%E5%BA%A6%E8%AE%A1) 加速度计

| API | 说明 |
| --- | --- |
| [uni.onAccelerometerChange](https://zh.uniapp.dcloud.io/api/system/accelerometer#onaccelerometerchange) | 监听加速度数据 |
| [uni.offAccelerometerChange](https://zh.uniapp.dcloud.io/api/system/accelerometer#offaccelerometerchange) | 取消监听加速度数据 |
| [uni.startAccelerometer](https://zh.uniapp.dcloud.io/api/system/accelerometer#startaccelerometer) | 开始监听加速度数据 |
| [uni.stopAccelerometer](https://zh.uniapp.dcloud.io/api/system/accelerometer#stopaccelerometer) | 停止监听加速度数据 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E7%BD%97%E7%9B%98) 罗盘

| API | 说明 |
| --- | --- |
| [uni.onCompassChange](https://zh.uniapp.dcloud.io/api/system/compass#oncompasschange) | 监听罗盘数据 |
| [uni.offCompassChange](https://zh.uniapp.dcloud.io/api/system/compass#offcompasschange) | 取消监听罗盘数据 |
| [uni.startCompass](https://zh.uniapp.dcloud.io/api/system/compass#startcompass) | 开始监听罗盘数据 |
| [uni.stopCompass](https://zh.uniapp.dcloud.io/api/system/compass#stopcompass) | 停止监听罗盘数据 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E9%99%80%E8%9E%BA%E4%BB%AA) 陀螺仪

| API | 说明 |
| --- | --- |
| [uni.onGyroscopeChange](https://zh.uniapp.dcloud.io/api/system/gyroscope#ongyroscopechange) | 监听陀螺仪数据 |
| [uni.startGyroscope](https://zh.uniapp.dcloud.io/api/system/gyroscope#startgyroscope) | 开始监听陀螺仪数据 |
| [uni.stopGyroscope](https://zh.uniapp.dcloud.io/api/system/gyroscope#stopgyroscope) | 停止监听陀螺仪数据 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%8B%A8%E6%89%93%E7%94%B5%E8%AF%9D) 拨打电话

| API | 说明 |
| --- | --- |
| [uni.makePhoneCall](https://zh.uniapp.dcloud.io/api/system/phone#makephonecall) | 拨打电话 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%89%AB%E7%A0%81) 扫码

| API | 说明 |
| --- | --- |
| [uni.scanCode](https://zh.uniapp.dcloud.io/api/system/barcode#scancode) | 扫码 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%89%AA%E5%88%87%E6%9D%BF) 剪切板

| API | 说明 |
| --- | --- |
| [uni.setClipboardData](https://zh.uniapp.dcloud.io/api/system/clipboard#setclipboarddata) | 设置剪贴板内容 |
| [uni.getClipboardData](https://zh.uniapp.dcloud.io/api/system/clipboard#getclipboarddata) | 获取剪贴板内容 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%B1%8F%E5%B9%95%E4%BA%AE%E5%BA%A6) 屏幕亮度

| API | 说明 |
| --- | --- |
| [uni.setScreenBrightness](https://zh.uniapp.dcloud.io/api/system/brightness#setscreenbrightness) | 设置屏幕亮度 |
| [uni.getScreenBrightness](https://zh.uniapp.dcloud.io/api/system/brightness#getscreenbrightness) | 获取屏幕亮度 |
| [uni.setKeepScreenOn](https://zh.uniapp.dcloud.io/api/system/brightness#setkeepscreenon) | 设置是否保持常亮状态 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E7%94%A8%E6%88%B7%E6%88%AA%E5%B1%8F%E4%BA%8B%E4%BB%B6) 用户截屏事件

| API | 说明 |
| --- | --- |
| [uni.onUserCaptureScreen](https://zh.uniapp.dcloud.io/api/system/capture-screen) | 监听用户截屏事件 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%8C%AF%E5%8A%A8) 振动

| API | 说明 |
| --- | --- |
| [uni.vibrate](https://zh.uniapp.dcloud.io/api/system/vibrate#vibrate) | 使手机发生振动 |
| [uni.vibrateLong](https://zh.uniapp.dcloud.io/api/system/vibrate#vibratelong) | 使手机发生较长时间的振动 |
| [uni.vibrateShort](https://zh.uniapp.dcloud.io/api/system/vibrate#vibrateshort) | 使手机发生较短时间的振动 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%89%8B%E6%9C%BA%E8%81%94%E7%B3%BB%E4%BA%BA) 手机联系人

| API | 说明 |
| --- | --- |
| [uni.addPhoneContact](https://zh.uniapp.dcloud.io/api/system/contact#addphonecontact) | 添加手机通讯录 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%93%9D%E7%89%99) 蓝牙

| API | 说明 |
| --- | --- |
| [uni.openBluetoothAdapter](https://zh.uniapp.dcloud.io/api/system/bluetooth#openbluetoothadapter) | 初始化蓝牙模块 |
| [uni.startBluetoothDevicesDiscovery](https://zh.uniapp.dcloud.io/api/system/bluetooth#startbluetoothdevicesdiscovery) | 搜寻附近的蓝牙外围设备 |
| [uni.onBluetoothDeviceFound](https://zh.uniapp.dcloud.io/api/system/bluetooth#onbluetoothdevicefound) | 监听寻找到新设备的事件 |
| [uni.stopBluetoothDevicesDiscovery](https://zh.uniapp.dcloud.io/api/system/bluetooth#stopbluetoothdevicesdiscovery) | 停止搜寻 |
| [uni.onBluetoothAdapterStateChange](https://zh.uniapp.dcloud.io/api/system/bluetooth#onbluetoothadapterstatechange) | 监听蓝牙适配器状态变化事件 |
| [uni.getConnectedBluetoothDevices](https://zh.uniapp.dcloud.io/api/system/bluetooth#getconnectedbluetoothdevices) | 根据 uuid 获取处于已连接状态的设备 |
| [uni.getBluetoothDevices](https://zh.uniapp.dcloud.io/api/system/bluetooth#getbluetoothdevices) | 获取已发现的蓝牙设备 |
| [uni.getBluetoothAdapterState](https://zh.uniapp.dcloud.io/api/system/bluetooth#getbluetoothadapterstate) | 获取本机蓝牙适配器状态 |
| [uni.closeBluetoothAdapter](https://zh.uniapp.dcloud.io/api/system/bluetooth#closebluetoothadapter) | 关闭蓝牙模块 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E4%BD%8E%E8%80%97%E8%93%9D%E7%89%99) 低耗蓝牙

| API | 说明 |
| --- | --- |
| [uni.writeBLECharacteristicValue](https://zh.uniapp.dcloud.io/api/system/ble#writeblecharacteristicvalue) | 向低功耗蓝牙设备特征值中写入二进制数据 |
| [uni.readBLECharacteristicValue](https://zh.uniapp.dcloud.io/api/system/ble#readblecharacteristicvalue) | 读取低功耗蓝牙设备的特征值的二进制数据值 |
| [uni.onBLEConnectionStateChange](https://zh.uniapp.dcloud.io/api/system/ble#onbleconnectionstatechange) | 监听低功耗蓝牙连接状态的改变事件 |
| [uni.onBLECharacteristicValueChange](https://zh.uniapp.dcloud.io/api/system/ble#onblecharacteristicvaluechange) | 监听低功耗蓝牙设备的特征值变化事件 |
| [uni.notifyBLECharacteristicValueChange](https://zh.uniapp.dcloud.io/api/system/ble#notifyblecharacteristicvaluechange) | 启用蓝牙低功耗设备特征值变化时的 notify 功能，订阅特征 |
| [uni.getBLEDeviceServices](https://zh.uniapp.dcloud.io/api/system/ble#getbledeviceservices) | 获取蓝牙设备所有服务(service) |
| [uni.getBLEDeviceCharacteristics](https://zh.uniapp.dcloud.io/api/system/ble#getbledevicecharacteristics) | 获取蓝牙设备某个服务中所有特征值(characteristic) |
| [uni.createBLEConnection](https://zh.uniapp.dcloud.io/api/system/ble#createbleconnection) | 连接低功耗蓝牙设备 |
| [uni.closeBLEConnection](https://zh.uniapp.dcloud.io/api/system/ble#closebleconnection) | 断开与低功耗蓝牙设备的连接 |

#### [#](https://zh.uniapp.dcloud.io/api/#ibeacon) iBeacon

| API | 说明 |
| --- | --- |
| [uni.onBeaconServiceChange](https://zh.uniapp.dcloud.io/api/system/ibeacon#onbeaconservicechange) | 监听 iBeacon 服务状态变化事件 |
| [uni.onBeaconUpdate](https://zh.uniapp.dcloud.io/api/system/ibeacon#onbeaconupdate) | 监听 iBeacon 设备更新事件 |
| [uni.getBeacons](https://zh.uniapp.dcloud.io/api/system/ibeacon#getbeacons) | 获取所有已搜索到的 iBeacon 设备 |
| [uni.startBeaconDiscovery](https://zh.uniapp.dcloud.io/api/system/ibeacon#startbeacondiscovery) | 停止搜索附近的 iBeacon 设备 |
| [uni.stopBeaconDiscovery](https://zh.uniapp.dcloud.io/api/system/ibeacon#stopbeacondiscovery) | 开始搜索附近的 iBeacon 设备 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E7%94%9F%E7%89%A9%E8%AE%A4%E8%AF%81) 生物认证

| API | 说明 |
| --- | --- |
| [uni.startSoterAuthentication](https://zh.uniapp.dcloud.io/api/system/authentication#startsoterauthentication) | 开始生物认证 |
| [uni.checkIsSupportSoterAuthentication](https://zh.uniapp.dcloud.io/api/system/authentication#checkissupportsoterauthentication) | 获取本机支持的生物认证方式 |
| [uni.checkIsSoterEnrolledInDevice](https://zh.uniapp.dcloud.io/api/system/authentication#checkissoterenrolledindevice) | 获取设备内是否录入如指纹等生物信息的接口 |

### [#](https://zh.uniapp.dcloud.io/api/#%E7%95%8C%E9%9D%A2) 界面

#### [#](https://zh.uniapp.dcloud.io/api/#%E4%BA%A4%E4%BA%92%E5%8F%8D%E9%A6%88) 交互反馈

| API | 说明 |
| --- | --- |
| [uni.showToast](https://zh.uniapp.dcloud.io/api/ui/prompt#showtoast) | 显示提示框 |
| [uni.showLoading](https://zh.uniapp.dcloud.io/api/ui/prompt#showloading) | 显示加载提示框 |
| [uni.hideToast](https://zh.uniapp.dcloud.io/api/ui/prompt#hidetoast) | 隐藏提示框 |
| [uni.hideLoading](https://zh.uniapp.dcloud.io/api/ui/prompt#hideloading) | 隐藏加载提示框 |
| [uni.showModal](https://zh.uniapp.dcloud.io/api/ui/prompt#showmodal) | 显示模态弹窗 |
| [uni.showActionSheet](https://zh.uniapp.dcloud.io/api/ui/prompt#showactionsheet) | 显示菜单列表 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%AE%BE%E7%BD%AE%E5%AF%BC%E8%88%AA%E6%9D%A1) 设置导航条

| API | 说明 |
| --- | --- |
| [uni.setNavigationBarTitle](https://zh.uniapp.dcloud.io/api/ui/navigationbar#setnavigationbartitle) | 设置当前页面标题 |
| [uni.setNavigationBarColor](https://zh.uniapp.dcloud.io/api/ui/navigationbar#setnavigationbarcolor) | 设置页面导航条颜色 |
| [uni.showNavigationBarLoading](https://zh.uniapp.dcloud.io/api/ui/navigationbar#shownavigationbarloading) | 显示导航条加载动画 |
| [uni.hideNavigationBarLoading](https://zh.uniapp.dcloud.io/api/ui/navigationbar#hidenavigationbarloading) | 隐藏导航条加载动画 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%AE%BE%E7%BD%AE-tabbar) 设置 TabBar

| API | 说明 |
| --- | --- |
| [uni.setTabBarItem](https://zh.uniapp.dcloud.io/api/ui/tabbar#settabbaritem) | 动态设置 tabBar 某一项的内容 |
| [uni.setTabBarStyle](https://zh.uniapp.dcloud.io/api/ui/tabbar#settabbarstyle) | 动态设置 tabBar 的整体样式 |
| [uni.hideTabBar](https://zh.uniapp.dcloud.io/api/ui/tabbar#hidetabbar) | 隐藏 tabBar |
| [uni.showTabBar](https://zh.uniapp.dcloud.io/api/ui/tabbar#showtabbar) | 显示 tabBar |
| [uni.setTabBarBadge](https://zh.uniapp.dcloud.io/api/ui/tabbar#settabbarbadge) | 为 tabBar 某一项的右上角添加文本 |
| [uni.removeTabBarBadge](https://zh.uniapp.dcloud.io/api/ui/tabbar#removetabbarbadge) | 移除 tabBar 某一项右上角的文本 |
| [uni.showTabBarRedDot](https://zh.uniapp.dcloud.io/api/ui/tabbar#showtabbarreddot) | 显示 tabBar 某一项的右上角的红点 |
| [uni.hideTabBarRedDot](https://zh.uniapp.dcloud.io/api/ui/tabbar#hidetabbarreddot) | 隐藏 tabBar 某一项的右上角的红点 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%83%8C%E6%99%AF) 背景

| API | 说明 |
| --- | --- |
| [uni.setBackgroundColor](https://zh.uniapp.dcloud.io/api/ui/bgcolor#setbackgroundcolor) | 动态设置窗口的背景色。 |
| [uni.setBackgroundTextStyle](https://zh.uniapp.dcloud.io/api/ui/bgcolor#setbackgroundtextstyle) | 动态设置下拉背景字体、loading 图的样式。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%8A%A8%E7%94%BB) 动画

| API | 说明 |
| --- | --- |
| [uni.createAnimation](https://zh.uniapp.dcloud.io/api/ui/animation#createanimation) | 创建一个动画实例 animation。调用实例的方法来描述动画。最后通过动画实例的 export 方法导出动画数据传递给组件的 animation 属性。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%BB%9A%E5%8A%A8) 滚动

| API | 说明 |
| --- | --- |
| [uni.pageScrollTo](https://zh.uniapp.dcloud.io/api/ui/scroll#pagescrollto) | 将页面滚动到目标位置。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E7%94%BB%E5%B8%83) 画布

| API | 说明 |
| --- | --- |
| [uni.createCanvasContext](https://zh.uniapp.dcloud.io/api/canvas/createCanvasContext) | 创建绘图上下文 |
| [uni.canvasToTempFilePath](https://zh.uniapp.dcloud.io/api/canvas/canvasToTempFilePath) | 将画布内容保存成文件 |
| [uni.canvasGetImageData](https://zh.uniapp.dcloud.io/api/canvas/canvasGetImageData) | 获取画布图像数据 |
| [uni.canvasPutImageData](https://zh.uniapp.dcloud.io/api/canvas/canvasPutImageData) | 设置画布图像数据 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E4%B8%8B%E6%8B%89%E5%88%B7%E6%96%B0) 下拉刷新

| API | 说明 |
| --- | --- |
| [onPullDownRefresh](https://zh.uniapp.dcloud.io/api/ui/pulldown#onpulldownrefresh) | 监听该页面用户下拉刷新事件 |
| [uni.startPullDownRefresh](https://zh.uniapp.dcloud.io/api/ui/pulldown#startpulldownrefresh) | 开始下拉刷新 |
| [uni.stopPullDownRefresh](https://zh.uniapp.dcloud.io/api/ui/pulldown#stoppulldownrefresh) | 停止当前页面下拉刷新 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%8A%82%E7%82%B9%E4%BF%A1%E6%81%AF) 节点信息

| API | 说明 |
| --- | --- |
| [uni.createSelectorQuery](https://zh.uniapp.dcloud.io/api/ui/nodes-info#createselectorquery) | 创建查询请求 |
| [selectorQuery.select](https://zh.uniapp.dcloud.io/api/ui/nodes-info#selectorquery-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 根据选择器选择单个节点 |
| [selectorQuery.selectAll](https://zh.uniapp.dcloud.io/api/ui/nodes-info#selectorquery-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 根据选择器选择全部节点 |
| [selectorQuery.selectViewport](https://zh.uniapp.dcloud.io/api/ui/nodes-info#selectorquery-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 选择显示区域 |
| [selectorQuery.exec](https://zh.uniapp.dcloud.io/api/ui/nodes-info#selectorquery-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 执行查询请求 |
| [nodesRef.boundingClientRect](https://zh.uniapp.dcloud.io/api/ui/nodes-info#nodesref-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 获取布局位置和尺寸 |
| [nodesRef.scrollOffset](https://zh.uniapp.dcloud.io/api/ui/nodes-info#nodesref-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 获取滚动位置 |
| [nodesRef.fields](https://zh.uniapp.dcloud.io/api/ui/nodes-info#nodesref-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 获取任意字段 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%8A%82%E7%82%B9%E5%B8%83%E5%B1%80%E7%9B%B8%E4%BA%A4%E7%8A%B6%E6%80%81) 节点布局相交状态

| API | 说明 |
| --- | --- |
| [uni.createIntersectionObserver](https://zh.uniapp.dcloud.io/api/ui/intersection-observer#createintersectionobserver) | 创建 IntersectionObserver 对象 |
| [intersectionObserver.relativeTo](https://zh.uniapp.dcloud.io/api/ui/intersection-observer#intersectionobserver-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 指定参照节点 |
| [intersectionObserver.relativeToViewport](https://zh.uniapp.dcloud.io/api/ui/intersection-observer#intersectionobserver-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 指定页面显示区域作为参照区域 |
| [intersectionObserver.observe](https://zh.uniapp.dcloud.io/api/ui/intersection-observer#intersectionobserver-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 指定目标节点并开始监听 |
| [intersectionObserver.disconnect](https://zh.uniapp.dcloud.io/api/ui/intersection-observer#intersectionobserver-%E5%AF%B9%E8%B1%A1%E7%9A%84%E6%96%B9%E6%B3%95%E5%88%97%E8%A1%A8) | 停止监听 |

### [#](https://zh.uniapp.dcloud.io/api/#%E9%A1%B5%E9%9D%A2%E5%92%8C%E8%B7%AF%E7%94%B1) 页面和路由

| API | 说明 |
| --- | --- |
| [uni.navigateTo](https://zh.uniapp.dcloud.io/api/router#navigateto) | 保留当前页面，跳转到应用内的某个页面，使用 uni.navigateBack 可以返回到原页面 |
| [uni.redirectTo](https://zh.uniapp.dcloud.io/api/router#redirectto) | 关闭当前页面，跳转到应用内的某个页面 |
| [uni.reLaunch](https://zh.uniapp.dcloud.io/api/router#relaunch) | 关闭所有页面，打开到应用内的某个页面 |
| [uni.switchTab](https://zh.uniapp.dcloud.io/api/router#switchtab) | 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面 |
| [uni.navigateBack](https://zh.uniapp.dcloud.io/api/router#navigateback) | 关闭当前页面，返回上一页面或多级页面 |

### [#](https://zh.uniapp.dcloud.io/api/#%E9%94%AE%E7%9B%98) 键盘

| API | 说明 |
| --- | --- |
| [uni.hideKeyboard](https://zh.uniapp.dcloud.io/api/key.html#hidekeyboard) | 隐藏已经显示的软键盘，如果软键盘没有显示则不做任何操作。 |
| [uni.onKeyboardHeightChange](https://zh.uniapp.dcloud.io/api/key.html#onkeyboardheightchange) | 监听键盘高度变化 |
| [uni.offKeyboardHeightChange](https://zh.uniapp.dcloud.io/api/key.html#offkeyboardheightchange) | 取消监听键盘高度变化事件 |
| [uni.getSelectedTextRange](https://zh.uniapp.dcloud.io/api/key#getselectedtextrange) | 在 input、textarea 等 focus 之后，获取输入框的光标位置 |

### [#](https://zh.uniapp.dcloud.io/api/#%E7%AC%AC%E4%B8%89%E6%96%B9%E6%9C%8D%E5%8A%A1) 第三方服务

| API | 说明 |
| --- | --- |
| [uni.getProvider](https://zh.uniapp.dcloud.io/api/plugins/provider#getprovider) | 获取服务供应商 |
| [uni.login](https://zh.uniapp.dcloud.io/api/plugins/login#login) | 登录 |
| [uni.getUserInfo](https://zh.uniapp.dcloud.io/api/plugins/login#getuserinfo) | 获取用户信息 |
| [uni.getuserprofile](https://zh.uniapp.dcloud.io/api/plugins/login#getuserprofile) | 获取用户信息。每次请求都会弹出授权窗口，用户同意后返回 userInfo |
| [uni.checkSession](https://zh.uniapp.dcloud.io/api/plugins/login#checkSession) | 检查登录状态是否过期 |
| [uni.preLogin](https://zh.uniapp.dcloud.io/api/plugins/login#prelogin) | 预登录 |
| [uni.closeAuthView](https://zh.uniapp.dcloud.io/api/plugins/login#closeauthview) | 关闭一键登录页面 |
| [uni.getCheckBoxState](https://zh.uniapp.dcloud.io/api/plugins/login#getcheckboxstate) | 获取一键登录条款勾选框状态 |
| [uni.getUniverifyManager](https://zh.uniapp.dcloud.io/api/plugins/login#getUniverifyManager) | 获取全局唯一的一键登录管理器 univerifyManager |
| [uni.share](https://zh.uniapp.dcloud.io/api/plugins/share#share) | 分享 |
| [uni.shareWithSystem](https://zh.uniapp.dcloud.io/api/plugins/share#sharewithsystem) | 使用系统分享 |
| [uni.requestPayment](https://zh.uniapp.dcloud.io/api/plugins/payment#requestpayment) | 支付 |
| [uni.onPushMessage](https://zh.uniapp.dcloud.io/api/plugins/push#onpushmessage) | 启动监听推送消息事件 |
| [uni.offPushMessage](https://zh.uniapp.dcloud.io/api/plugins/push#offpushmessage) | 关闭推送消息监听事件 |

### [#](https://zh.uniapp.dcloud.io/api/#%E5%B9%BF%E5%91%8A) 广告

| API | 说明 |
| --- | --- |
| [激励视频广告](https://zh.uniapp.dcloud.io/api/a-d/rewarded-video.html) | 激励视频广告，是 cpm 收益最高的广告形式 |
| [全屏视频广告](https://zh.uniapp.dcloud.io/api/a-d/full-screen-video.html) | 全屏视频广告 |
| [内容联盟广告](https://zh.uniapp.dcloud.io/api/a-d/content-page.html) | 内容联盟广告 |
| [插屏广告](https://zh.uniapp.dcloud.io/api/a-d/interstitial.html) | 插屏广告 |
| [互动游戏](https://zh.uniapp.dcloud.io/api/a-d/interactive.html) | 互动游戏是 DCloud 联合三方服务商为开发者提供新的广告场景增值服务 |

### [#](https://zh.uniapp.dcloud.io/api/#%E5%B9%B3%E5%8F%B0%E6%89%A9%E5%B1%95) 平台扩展

| API | 说明 |
| --- | --- |
| [uni.requireNativePlugin](https://zh.uniapp.dcloud.io/plugin/native-plugin.html#requirenativeplugin) | 引入 App 原生插件 |

### [#](https://zh.uniapp.dcloud.io/api/#%E5%85%B6%E4%BB%96) 其他

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%8E%88%E6%9D%83) 授权

| API | 说明 |
| --- | --- |
| [uni.authorize](https://zh.uniapp.dcloud.io/api/other/authorize#authorize) | 提前向用户发起授权请求 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%AE%BE%E7%BD%AE) 设置

| API | 说明 |
| --- | --- |
| [uni.openSetting](https://zh.uniapp.dcloud.io/api/other/setting#opensetting) | 调起客户端小程序设置界面，返回用户设置的操作结果。 |
| [uni.getSetting](https://zh.uniapp.dcloud.io/api/other/setting#getsetting) | 获取用户的当前设置。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%94%B6%E8%B4%A7%E5%9C%B0%E5%9D%80) 收货地址

| API | 说明 |
| --- | --- |
| [uni.chooseAddress](https://zh.uniapp.dcloud.io/api/other/choose-address#chooseaddress) | 获取用户收货地址 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%8E%B7%E5%8F%96%E5%8F%91%E7%A5%A8%E6%8A%AC%E5%A4%B4) 获取发票抬头

| API | 说明 |
| --- | --- |
| [uni.chooseInvoiceTitle](https://zh.uniapp.dcloud.io/api/other/invoice-title#chooseinvoicetitle) | 选择用户的发票抬头，需要用户授权 scope.invoiceTitle。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%B0%8F%E7%A8%8B%E5%BA%8F%E8%B7%B3%E8%BD%AC) 小程序跳转

| API | 说明 |
| --- | --- |
| [uni.navigateToMiniProgram](https://zh.uniapp.dcloud.io/api/other/open-miniprogram#navigatetominiprogram) | 打开另一个小程序。 |
| [uni.navigateBackMiniProgram](https://zh.uniapp.dcloud.io/api/other/open-miniprogram#navigatebackminiprogram) | 跳转回上一个小程序，只有当另一个小程序跳转到当前小程序时才会能调用成功。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E6%A8%A1%E6%9D%BF%E6%B6%88%E6%81%AF) 模板消息

| API | 说明 |
| --- | --- |
| [addTemplate](https://zh.uniapp.dcloud.io/api/other/template#addtemplate) | 组合模板并添加至帐号下的个人模板库。 |
| [deleteTemplate](https://zh.uniapp.dcloud.io/api/other/template#deletetemplate) | 删除帐号下的某个模板。 |
| [getTemplateLibraryById](https://zh.uniapp.dcloud.io/api/other/template#gettemplatelibrarybyid) | 获取模板库某个模板标题下关键词库。 |
| [getTemplateLibraryList](https://zh.uniapp.dcloud.io/api/other/template#gettemplatelibrarylist) | 获取 APP 模板库标题列表 |
| [getTemplateList](https://zh.uniapp.dcloud.io/api/other/template#gettemplatelist) | 获取帐号下已存在的模板列表。 |
| [sendTemplateMessage](https://zh.uniapp.dcloud.io/api/other/template#sendtemplatemessage) | 发送模板消息 |
| [alipay.open.app.mini.templatemessage.send](https://zh.uniapp.dcloud.io/api/other/template#alipayopenappminitemplatemessagesend) | 支付宝小程序通过 openapi 给用户触达消息，主要为支付后的触达（通过消费 id）和用户提交表单后的触达（通过 formId）。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E5%B0%8F%E7%A8%8B%E5%BA%8F%E6%9B%B4%E6%96%B0) 小程序更新

| API | 说明 |
| --- | --- |
| [uni.getUpdateManager](https://zh.uniapp.dcloud.io/api/other/update#getupdatemanager) | 返回全局唯一的版本更新管理器对象： updateManager，用于管理小程序更新。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%B0%83%E8%AF%95) 调试

| API | 说明 |
| --- | --- |
| [uni.setEnableDebug](https://zh.uniapp.dcloud.io/api/other/set-enable-debug#setenabledebug) | 设置是否打开调试开关。此开关对正式版也能生效。 |

#### [#](https://zh.uniapp.dcloud.io/api/#%E8%8E%B7%E5%8F%96%E7%AC%AC%E4%B8%89%E6%96%B9%E5%B9%B3%E5%8F%B0%E6%95%B0%E6%8D%AE) 获取第三方平台数据

| API | 说明 |
| --- | --- |
| [uni.getExtConfig](https://zh.uniapp.dcloud.io/api/other/get-extconfig#getextconfig) | 获取第三方平台自定义的数据字段。 |
| [uni.getExtConfigSync](https://zh.uniapp.dcloud.io/api/other/get-extconfig#getextconfigsync) | uni.getExtConfig 的同步版本。 |

因文档同步原因，本页面列出的 API 可能不全。如在本文未找到相关 API，可以在左侧树中寻找或使用文档右上角的搜索功能。