// 云函数入口文件
const cloud = require('wx-server-sdk')
const cloudbase = require('@cloudbase/js-sdk')
const adapter = require('@cloudbase/adapter-node')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 初始化CloudBase AI SDK
const { sessionStorage } = adapter.genAdapter()
cloudbase.useAdapters(adapter)

const app = cloudbase.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数checkText被调用，事件参数:', event)
  console.log('上下文信息:', context)
  
  try {
    const wxContext = cloud.getWXContext()
    console.log('用户信息:', {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    })

    // 获取请求参数
    const { text, user_id, session_id } = event
    
    // 参数验证
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return {
        code: 400,
        message: '文本内容不能为空',
        error: 'text parameter is required and cannot be empty',
        timestamp: new Date().toISOString()
      }
    }

    if (text.length > 2000) {
      return {
        code: 400,
        message: '文本内容超过最大长度限制(2000字符)',
        error: 'text content exceeds maximum length limit',
        timestamp: new Date().toISOString()
      }
    }

    // 生成唯一的核查ID
    const checkId = `check_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    console.log('开始文本核查，核查ID:', checkId)
    console.log('待核查文本:', text)

    // 初始化AI SDK
    const auth = app.auth({
      storage: sessionStorage,
      captchaOptions: {
        openURIWithCallback: () => console.log("open uri with callback"),
      },
    })

    await auth.signInAnonymously() // 匿名登录
    const ai = await app.ai()

    // 创建AI模型实例 - 使用DeepSeek模型
    const model = ai.createModel("deepseek")

    // 设置系统提示词 - 专门用于信息核查
    const systemPrompt = `你是一个专业的信息核查助手，负责分析用户提供的文本信息的真实性。请按照以下要求进行分析：

1. 仔细分析文本内容，识别关键信息和事实声明
2. 基于你的知识库判断信息的可信度
3. 提供简洁明确的核查结果
4. 如果信息涉及时效性内容，请说明需要进一步验证

请严格按照以下JSON格式返回结果：
{
  "status": "supported|disputed|insufficient",
  "summary": "核查结果的简要说明",
  "confidence": 0.0-1.0之间的数值,
  "analysis": "详细分析过程",
  "key_points": ["关键信息点1", "关键信息点2"],
  "recommendations": ["建议1", "建议2"]
}

状态说明：
- supported: 信息可信，有证据支持
- disputed: 信息存疑，有证据反驳或矛盾
- insufficient: 信息不足，无法做出明确判断`

    // 调用AI模型进行文本分析
    const aiResponse = await model.streamText({
      model: "deepseek-r1",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: `请核查以下信息的真实性：\n\n${text}` }
      ],
      temperature: 0.3, // 降低随机性，提高一致性
      max_tokens: 1500
    })

    // 收集AI响应
    let aiResult = ''
    for await (let chunk of aiResponse.textStream) {
      aiResult += chunk
    }

    console.log('AI分析结果:', aiResult)

    // 解析AI返回的JSON结果
    let analysisResult
    try {
      // 尝试提取JSON部分
      const jsonMatch = aiResult.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('无法解析AI返回的JSON格式')
      }
    } catch (parseError) {
      console.error('解析AI结果失败:', parseError)
      // 如果解析失败，使用默认结果
      analysisResult = {
        status: 'insufficient',
        summary: '分析过程中遇到技术问题，建议人工核查',
        confidence: 0.5,
        analysis: aiResult,
        key_points: ['技术分析异常'],
        recommendations: ['建议通过其他渠道验证信息真实性']
      }
    }

    // 模拟生成一些示例来源（实际项目中应该通过搜索API获取）
    const mockSources = [
      {
        title: '相关新闻报道',
        url: 'https://example.com/news/1',
        credibility: 0.8,
        publish_date: '2024-01-15'
      },
      {
        title: '官方声明',
        url: 'https://example.com/official/1',
        credibility: 0.9,
        publish_date: '2024-01-14'
      }
    ]

    // 构建返回结果
    const result = {
      check_id: checkId,
      status: analysisResult.status,
      summary: analysisResult.summary,
      confidence: analysisResult.confidence,
      publish_time: null, // 需要通过搜索API获取
      sources: analysisResult.status !== 'insufficient' ? mockSources : [],
      evidences: analysisResult.key_points || [],
      analysis_time: new Date().toISOString(),
      detailed_analysis: analysisResult.analysis,
      recommendations: analysisResult.recommendations || []
    }

    console.log('核查完成，结果:', result)

    return {
      code: 200,
      message: 'success',
      data: result,
      timestamp: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('文本核查失败:', error)
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}
