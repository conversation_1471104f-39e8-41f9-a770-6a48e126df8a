# AI赋能群聊信息核查助手 - API接口文档

## 概述

本文档描述了前端uni-app小程序与后端服务之间的API接口规范。前端主要通过`uni.request`和`uni.uploadFile`等uni-app API与后端进行数据交互。

## 基础信息

- **协议**: HTTPS
- **数据格式**: JSON (除文件上传接口使用multipart/form-data)
- **字符编码**: UTF-8
- **请求头**: 
  - `Content-Type: application/json` (JSON接口)
  - `Content-Type: multipart/form-data` (文件上传接口)

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体业务数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## API接口列表

### 1. 文本内容核查

**接口地址**: `POST /api/v1/check/text`

**功能描述**: 接收用户直接输入的文本内容进行信息核查

**请求参数**:
```json
{
  "text": "待核查的文本内容",
  "user_id": "用户唯一标识(可选)",
  "session_id": "会话ID(可选)"
}
```

**参数说明**:
- `text` (string, 必填): 待核查的文本内容，最大长度2000字符
- `user_id` (string, 可选): 用户唯一标识，用于用户行为分析
- `session_id` (string, 可选): 会话ID，用于关联同一次核查会话

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "check_id": "核查任务唯一标识",
    "status": "supported|disputed|insufficient",
    "summary": "核查结果摘要",
    "confidence": 0.85,
    "publish_time": "2024-02-15",
    "sources": [
      {
        "title": "来源标题",
        "url": "https://example.com/article1",
        "credibility": 0.9,
        "publish_date": "2024-02-15"
      }
    ],
    "evidences": [
      "支撑证据1",
      "支撑证据2"
    ],
    "analysis_time": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**状态码说明**:
- `supported`: 信息可信，有证据支持
- `disputed`: 信息存疑，有证据反驳
- `insufficient`: 信息不足，无法判断

### 2. 图片内容核查

**接口地址**: `POST /api/v1/check/image`

**功能描述**: 接收用户上传的图片文件，通过OCR提取文本后进行信息核查

**请求方式**: multipart/form-data

**请求参数**:
- `image_file` (file, 必填): 图片文件，支持jpg、png、gif格式，最大5MB
- `user_id` (string, 可选): 用户唯一标识
- `session_id` (string, 可选): 会话ID

**响应数据**: 与文本核查接口相同，额外包含OCR结果
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "check_id": "核查任务唯一标识",
    "ocr_result": {
      "extracted_text": "OCR提取的文本内容",
      "confidence": 0.95,
      "regions": [
        {
          "text": "文本片段",
          "bbox": [x1, y1, x2, y2],
          "confidence": 0.98
        }
      ]
    },
    "status": "supported|disputed|insufficient",
    "summary": "核查结果摘要",
    // ... 其他字段同文本核查接口
  }
}
```

### 3. 用户反馈提交

**接口地址**: `POST /api/v1/feedback`

**功能描述**: 接收用户对核查结果的反馈评价

**请求参数**:
```json
{
  "check_id": "核查任务唯一标识",
  "rating": "accurate|inaccurate",
  "comment": "用户评论(可选)",
  "user_id": "用户唯一标识(可选)"
}
```

**参数说明**:
- `check_id` (string, 必填): 对应核查任务的唯一标识
- `rating` (string, 必填): 用户评价，accurate(准确) 或 inaccurate(不准确)
- `comment` (string, 可选): 用户的详细评论或建议
- `user_id` (string, 可选): 用户唯一标识

**响应数据**:
```json
{
  "code": 200,
  "message": "反馈提交成功",
  "data": {
    "feedback_id": "反馈记录ID",
    "submitted_at": "2024-01-01T12:00:00Z"
  }
}
```

### 4. 历史记录查询 (可选)

**接口地址**: `GET /api/v1/history`

**功能描述**: 获取用户的云端历史核查记录(如果实现云端存储)

**请求参数**:
- `user_id` (string, 必填): 用户唯一标识
- `page` (number, 可选): 页码，默认1
- `limit` (number, 可选): 每页数量，默认20，最大100

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "records": [
      {
        "check_id": "核查任务ID",
        "content": "核查内容摘要",
        "status": "supported|disputed|insufficient", 
        "check_time": "2024-01-01T12:00:00Z",
        "result_summary": "结果摘要"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权访问 | 检查用户身份验证 |
| 403 | 禁止访问 | 检查用户权限 |
| 413 | 请求实体过大 | 减小上传文件大小或文本长度 |
| 429 | 请求频率过高 | 降低请求频率，实施客户端限流 |
| 500 | 服务器内部错误 | 稍后重试，持续失败请联系技术支持 |
| 502 | 网关错误 | 稍后重试 |
| 503 | 服务不可用 | 稍后重试 |

## 前端调用示例

### 使用uni.request调用文本核查接口

```javascript
// 文本核查
const checkText = async (text) => {
  try {
    const response = await uni.request({
      url: 'https://api.example.com/api/v1/check/text',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        text: text,
        user_id: 'user_123',
        session_id: 'session_456'
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('文本核查失败:', error);
    throw error;
  }
};
```

### 使用uni.uploadFile调用图片核查接口

```javascript
// 图片核查
const checkImage = async (filePath) => {
  try {
    const response = await uni.uploadFile({
      url: 'https://api.example.com/api/v1/check/image',
      filePath: filePath,
      name: 'image_file',
      formData: {
        user_id: 'user_123',
        session_id: 'session_456'
      }
    });
    
    const result = JSON.parse(response.data);
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('图片核查失败:', error);
    throw error;
  }
};
```

### 提交用户反馈

```javascript
// 提交反馈
const submitFeedback = async (checkId, rating, comment = '') => {
  try {
    const response = await uni.request({
      url: 'https://api.example.com/api/v1/feedback',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        check_id: checkId,
        rating: rating,
        comment: comment,
        user_id: 'user_123'
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('反馈提交失败:', error);
    throw error;
  }
};
```

## 注意事项

1. **请求频率限制**: 建议实施客户端请求频率限制，避免过于频繁的API调用
2. **错误处理**: 前端需要妥善处理各种错误情况，提供友好的用户提示
3. **数据缓存**: 可以考虑对核查结果进行本地缓存，提升用户体验
4. **隐私保护**: 上传的图片和文本内容涉及用户隐私，需要严格按照隐私政策处理
5. **超时处理**: 核查过程可能耗时较长，建议设置合适的超时时间并提供加载状态提示

## 前端数据模型

### 核查结果数据模型
```typescript
interface CheckResult {
  check_id: string;
  status: 'supported' | 'disputed' | 'insufficient';
  summary: string;
  confidence: number;
  publish_time?: string;
  sources: Source[];
  evidences: string[];
  analysis_time: string;
  ocr_result?: OCRResult;
}

interface Source {
  title: string;
  url: string;
  credibility: number;
  publish_date?: string;
}

interface OCRResult {
  extracted_text: string;
  confidence: number;
  regions: OCRRegion[];
}

interface OCRRegion {
  text: string;
  bbox: [number, number, number, number];
  confidence: number;
}
```

### 历史记录数据模型
```typescript
interface HistoryItem {
  id: string;
  content: string;
  status: 'supported' | 'disputed' | 'insufficient';
  checkTime: number;
  result?: CheckResult;
}
```

## 前端实现要点

### 1. 网络请求封装

建议封装统一的API请求方法：

```javascript
// utils/api.js
const BASE_URL = 'https://api.example.com';

const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.data.code === 200) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.message));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

export { request };
```

### 2. 本地存储管理

历史记录的本地存储实现：

```javascript
// utils/storage.js
const STORAGE_KEY = 'checkHistory';

export const saveHistoryItem = (item) => {
  try {
    const history = getHistory();
    history.unshift({
      ...item,
      id: Date.now().toString(),
      checkTime: Date.now()
    });

    // 限制历史记录数量，最多保存100条
    if (history.length > 100) {
      history.splice(100);
    }

    uni.setStorageSync(STORAGE_KEY, JSON.stringify(history));
  } catch (error) {
    console.error('保存历史记录失败:', error);
  }
};

export const getHistory = () => {
  try {
    const history = uni.getStorageSync(STORAGE_KEY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('获取历史记录失败:', error);
    return [];
  }
};

export const clearHistory = () => {
  try {
    uni.removeStorageSync(STORAGE_KEY);
  } catch (error) {
    console.error('清空历史记录失败:', error);
  }
};
```

### 3. 图片处理

图片选择和上传的统一处理：

```javascript
// utils/image.js
export const chooseImage = () => {
  return new Promise((resolve, reject) => {
    // 优先使用uni-app标准API
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        resolve(res.tempFilePaths[0]);
      },
      fail: (err) => {
        // QQ小程序特殊处理
        // #ifdef MP-QQ
        if (typeof qq !== 'undefined' && qq.chooseMessageFile) {
          qq.chooseMessageFile({
            count: 1,
            type: 'image',
            success: (res) => {
              if (res.tempFiles && res.tempFiles.length > 0) {
                resolve(res.tempFiles[0].path);
              } else {
                reject(new Error('未选择图片'));
              }
            },
            fail: reject
          });
        } else {
          reject(err);
        }
        // #endif

        // #ifndef MP-QQ
        reject(err);
        // #endif
      }
    });
  });
};
```

## 云函数接口 (当前测试用)

### 测试云函数 - add

**函数名**: `add`

**功能**: 简单的加法运算，用于测试云函数调用

**调用方式**:
```javascript
wx.cloud.callFunction({
  name: 'add',
  data: {
    a: 1,
    b: 2
  },
  success: (res) => {
    console.log('计算结果:', res.result.sum);
  },
  fail: (err) => {
    console.error('调用失败:', err);
  }
});
```

**返回数据**:
```json
{
  "sum": 3,
  "eventParams": {"a": 1, "b": 2},
  "userInfo": {
    "openid": "用户openid",
    "appid": "小程序appid"
  },
  "envInfo": {
    "envId": "cloud1-4gqrn7kua46d9633",
    "callTime": "2024-01-01T12:00:00.000Z"
  }
}
```

## 后端实现建议

### 1. 技术栈推荐
- **运行时**: Node.js + Express.js 或 Python + FastAPI
- **部署方式**: Serverless (腾讯云SCF/阿里云FC) 或 容器化部署
- **数据库**: MongoDB/PostgreSQL (用户反馈、配置管理)
- **文件存储**: 对象存储服务 (OSS/COS)

### 2. 核心处理流程
1. **请求接收**: API网关接收并验证请求
2. **内容提取**: 图片OCR处理，文本预处理
3. **AI分析**: LLM理解、搜索查询生成
4. **网络搜索**: 调用搜索引擎API获取相关信息
5. **结果分析**: 真伪判断、来源追溯、时间估算
6. **响应返回**: 格式化结果并返回前端

### 3. 性能优化建议
- 实施请求缓存机制，避免重复分析相同内容
- 使用异步处理，提高并发能力
- 对AI服务调用进行超时控制和重试机制
- 实施分级处理，优先处理简单查询

### 4. 安全考虑
- API接口鉴权和限流
- 用户输入内容安全过滤
- 敏感信息脱敏处理
- HTTPS加密传输

## 测试用例

### 1. 文本核查测试
```javascript
// 测试用例1: 正常文本核查
const testTextCheck = async () => {
  const result = await checkText("某某地区发生地震，震级7.0级");
  console.log('核查结果:', result);
};

// 测试用例2: 空文本处理
const testEmptyText = async () => {
  try {
    await checkText("");
  } catch (error) {
    console.log('预期错误:', error.message);
  }
};
```

### 2. 图片核查测试
```javascript
// 测试用例: 图片OCR和核查
const testImageCheck = async () => {
  const imagePath = await chooseImage();
  const result = await checkImage(imagePath);
  console.log('OCR结果:', result.ocr_result);
  console.log('核查结果:', result);
};
```

### 3. 反馈提交测试
```javascript
// 测试用例: 用户反馈
const testFeedback = async () => {
  const result = await submitFeedback('check_123', 'accurate', '结果很准确');
  console.log('反馈提交结果:', result);
};
```

## 版本更新记录

- **v1.0.0** (2024-01-01): 初始版本，包含基础核查功能
- **v1.1.0** (计划): 增加批量核查、历史记录云同步功能
- **v1.2.0** (计划): 增加用户偏好设置、可信来源自定义功能
