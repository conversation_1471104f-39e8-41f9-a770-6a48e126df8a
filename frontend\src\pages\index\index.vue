<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">请输入需要核查的内容或上传图片</text>
			
			<!-- 文本输入区域 -->
			<uni-easyinput
				v-model="inputText"
				type="textarea"
				placeholder="请输入或粘贴需要核查的文本内容"
				:maxlength="2000"
				:autoHeight="true"
			/>
			
			<!-- 图片上传区域 -->
			<view class="image-upload">
				<uni-file-picker
					v-model="imageFiles"
					fileMediatype="image"
					mode="grid"
					:limit="1"
					@select="selectImage"
					@progress="uploadProgress"
					@success="uploadSuccess"
					@fail="uploadFail"
				/>
			</view>
			
			<!-- 平台特定的图片选择按钮 -->
			<!-- #ifdef MP-QQ -->
			<button class="platform-btn" @click="chooseMessageFile">从QQ会话选择图片</button>
			<!-- #endif -->
			
			<!-- 测试按钮 -->
			<button 
				type="button" 
				@click="testAddFunction"
				class="test-btn"
			>
				测试云函数
			</button>
			
			<!-- 提交按钮 -->
			<button 
				type="button" 
				:disabled="!canSubmit"
				@click="handleSubmit"
				class="submit-btn"
			>
				开始核查
			</button>
		</uni-card>
		
		<!-- 加载状态 -->
		<uni-popup ref="loadingPopup" type="center">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</uni-popup>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue'

declare const wx: {
  cloud: {
    DYNAMIC_CURRENT_ENV: string
    init: (options: { env: string; traceUser: boolean }) => void
    callFunction: (options: {
      name: string
      data: any
      success?: (res: any) => void
      fail?: (err: any) => void
    }) => void
  }
}

declare const qq: {
  chooseMessageFile: (options: {
    count: number
    type: string
    success?: (res: any) => void
    fail?: (err: any) => void
  }) => void
}

interface CloudFunctionResult {
  result: {
    sum: number
  }
}

interface FileItem {
  url: string
  name: string
  size: number
}

export default defineComponent({
	name: 'Index',
	setup() {
		// 初始化云开发
		onMounted(() => {
			wx.cloud.init({
				env: "cloud1-4gqrn7kua46d9633", // 使用当前环境
				traceUser: true
			})
		})
		
		const inputText = ref('')
		const imageFiles = ref<FileItem[]>([])
		const loadingPopup = ref<any>(null)
		const loadingText = {
			contentdown: '上拉显示更多',
			contentrefresh: '正在加载...',
			contentnomore: '没有更多数据了'
		}
		
		// 计算是否可以提交
		const canSubmit = computed(() => {
			return inputText.value.trim().length > 0 || imageFiles.value.length > 0
		})
		
		// 选择图片回调
		const selectImage = (e: { tempFiles: FileItem[] }) => {
			console.log('选择图片:', e)
		}
		
		// 上传进度回调
		const uploadProgress = (e: { progress: number }) => {
			console.log('上传进度:', e)
		}
		
		// 上传成功回调
		const uploadSuccess = (e: { file: FileItem }) => {
			console.log('上传成功:', e)
		}
		
		// 上传失败回调
		const uploadFail = (e: { errMsg: string }) => {
			console.log('上传失败:', e)
			uni.showToast({
				title: '图片上传失败',
				icon: 'none'
			})
		}
		
		// QQ平台特定的从会话选择图片
		const chooseMessageFile = () => {
			// #ifdef MP-QQ
			qq.chooseMessageFile({
				count: 1,
				type: 'image',
				success: (res: { tempFiles: Array<{ path: string; name: string; size: number }> }) => {
					console.log('从会话选择图片成功:', res)
					const tempFiles = res.tempFiles
					if (tempFiles && tempFiles.length > 0) {
						imageFiles.value = tempFiles.map(file => ({
							url: file.path,
							name: file.name,
							size: file.size
						}))
					}
				},
				fail: (err: { errMsg: string }) => {
					console.error('从会话选择图片失败:', err)
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			})
			// #endif
		}
		
		// 提交处理
		const handleSubmit = async () => {
			try {
				// 显示加载状态
				if (loadingPopup.value) {
					loadingPopup.value.open()
				}

				let checkResult = null

				// 判断是文本核查还是图片核查
				if (inputText.value.trim().length > 0) {
					// 文本核查
					console.log('开始文本核查:', inputText.value)

					const textResult = await wx.cloud.callFunction({
						name: 'checkText',
						data: {
							text: inputText.value.trim(),
							user_id: 'user_' + Date.now(),
							session_id: 'session_' + Date.now()
						}
					})

					if (textResult.result.code === 200) {
						checkResult = textResult.result.data
						console.log('文本核查成功:', checkResult)
					} else {
						throw new Error(textResult.result.message || '文本核查失败')
					}

				} else if (imageFiles.value.length > 0) {
					// 图片核查
					console.log('开始图片核查:', imageFiles.value[0])

					// 首先上传图片到云存储
					const uploadResult = await wx.cloud.uploadFile({
						cloudPath: `check-images/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`,
						filePath: imageFiles.value[0].url
					})

					console.log('图片上传成功:', uploadResult.fileID)

					// 调用图片核查云函数
					const imageResult = await wx.cloud.callFunction({
						name: 'checkImage',
						data: {
							fileID: uploadResult.fileID,
							user_id: 'user_' + Date.now(),
							session_id: 'session_' + Date.now()
						}
					})

					if (imageResult.result.code === 200) {
						checkResult = imageResult.result.data
						console.log('图片核查成功:', checkResult)
					} else {
						throw new Error(imageResult.result.message || '图片核查失败')
					}
				}

				if (checkResult) {
					// 保存到本地历史记录
					const historyItem = {
						id: checkResult.check_id,
						content: inputText.value.trim() || '图片内容核查',
						status: checkResult.status,
						checkTime: Date.now(),
						result: checkResult
					}

					try {
						const existingHistory = uni.getStorageSync('checkHistory') || '[]'
						const history = JSON.parse(existingHistory)
						history.unshift(historyItem)

						// 限制历史记录数量
						if (history.length > 100) {
							history.splice(100)
						}

						uni.setStorageSync('checkHistory', JSON.stringify(history))
						console.log('历史记录保存成功')
					} catch (storageError) {
						console.error('保存历史记录失败:', storageError)
					}

					// 跳转到结果页面，传递结果数据
					uni.navigateTo({
						url: '/pages/result/result',
						success: (res) => {
							// 通过事件通道传递数据
							if (res.eventChannel) {
								res.eventChannel.emit('checkResult', checkResult)
							}
						}
					})
				}

			} catch (error) {
				console.error('核查失败:', error)
				uni.showToast({
					title: error.message || '核查失败，请重试',
					icon: 'none',
					duration: 3000
				})
			} finally {
				// 关闭加载状态
				if (loadingPopup.value) {
					loadingPopup.value.close()
				}
			}
		}
		
		// 测试云函数
		const testAddFunction = () => {
			console.log('开始调用云函数add...')
			console.log('当前环境:', wx.cloud.DYNAMIC_CURRENT_ENV)
			
			wx.cloud.callFunction({
				name: 'add',
				data: {
					a: 1,
					b: 2,
				},
				success: (res: CloudFunctionResult) => {
					console.log('云函数调用成功:', res)
					console.log('完整响应:', JSON.stringify(res, null, 2))
					uni.showToast({
						title: `计算结果: ${res.result.sum}`, 
						icon: 'none'
					})
				},
				fail: (err: { errMsg: string }) => {
					console.error('云函数调用失败:', err)
					console.error('完整错误:', JSON.stringify(err, null, 2))
					uni.showToast({
						title: `调用失败: ${err.errMsg}`,
						icon: 'none'
					})
				}
			})
		}
		
		return {
			inputText,
			imageFiles,
			loadingPopup,
			loadingText,
			canSubmit,
			selectImage,
			uploadProgress,
			uploadSuccess,
			uploadFail,
			chooseMessageFile,
			handleSubmit,
			testAddFunction
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.image-upload {
		margin: 20rpx 0;
	}
	
	.platform-btn {
		margin: 20rpx 0;
		background-color: #12B7F5;
		color: #FFFFFF;
		
		&:active {
			background-color: #0E9BD9;
		}
	}
	
	.test-btn {
		margin: 20rpx 0;
		background-color: #4CAF50;
		color: #FFFFFF;
		
		&:active {
			background-color: #3E8E41;
		}
	}
	
	.submit-btn {
		margin-top: 30rpx;
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx;
		background-color: rgba(0, 0, 0, 0.8);
		border-radius: 16rpx;

		.loading-spinner {
			width: 60rpx;
			height: 60rpx;
			border: 4rpx solid #f3f3f3;
			border-top: 4rpx solid #007AFF;
			border-radius: 50%;
			animation: spin 1s linear infinite;
		}

		.loading-text {
			margin-top: 20rpx;
			color: white;
			font-size: 28rpx;
		}
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
}
</style>
