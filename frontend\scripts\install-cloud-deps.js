const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔧 开始安装云函数依赖...')

const cloudfunctionsDir = path.join(__dirname, '../cloudfunctions')

// 检查云函数目录是否存在
if (!fs.existsSync(cloudfunctionsDir)) {
  console.error('❌ 云函数目录不存在:', cloudfunctionsDir)
  process.exit(1)
}

// 获取所有云函数目录
const cloudFunctions = fs.readdirSync(cloudfunctionsDir).filter(item => {
  const itemPath = path.join(cloudfunctionsDir, item)
  return fs.statSync(itemPath).isDirectory()
})

console.log('📁 发现云函数:', cloudFunctions.join(', '))

// 为每个云函数安装依赖
cloudFunctions.forEach(functionName => {
  const functionDir = path.join(cloudfunctionsDir, functionName)
  const packageJsonPath = path.join(functionDir, 'package.json')
  
  console.log(`\n📦 处理云函数: ${functionName}`)
  
  // 检查package.json是否存在
  if (!fs.existsSync(packageJsonPath)) {
    console.warn(`⚠️ ${functionName} 缺少package.json，跳过`)
    return
  }
  
  try {
    // 读取package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    console.log(`   依赖: ${Object.keys(packageJson.dependencies || {}).join(', ')}`)
    
    // 切换到云函数目录并安装依赖
    process.chdir(functionDir)
    
    // 删除现有的node_modules和package-lock.json
    const nodeModulesPath = path.join(functionDir, 'node_modules')
    const packageLockPath = path.join(functionDir, 'package-lock.json')
    
    if (fs.existsSync(nodeModulesPath)) {
      fs.rmSync(nodeModulesPath, { recursive: true, force: true })
      console.log('   🗑️ 清理旧的node_modules')
    }
    
    if (fs.existsSync(packageLockPath)) {
      fs.unlinkSync(packageLockPath)
      console.log('   🗑️ 清理旧的package-lock.json')
    }
    
    // 安装依赖
    console.log('   ⬇️ 安装依赖...')
    execSync('npm install', { 
      stdio: 'pipe',
      cwd: functionDir
    })
    
    // 验证wx-server-sdk是否安装成功
    const wxSdkPath = path.join(functionDir, 'node_modules', 'wx-server-sdk')
    if (fs.existsSync(wxSdkPath)) {
      console.log('   ✅ wx-server-sdk 安装成功')
    } else {
      console.error('   ❌ wx-server-sdk 安装失败')
    }
    
    console.log(`   ✅ ${functionName} 依赖安装完成`)
    
  } catch (error) {
    console.error(`   ❌ ${functionName} 依赖安装失败:`, error.message)
  }
})

// 回到原始目录
process.chdir(path.join(__dirname, '..'))

console.log('\n🎉 云函数依赖安装完成!')
console.log('\n💡 提示:')
console.log('1. 请在微信开发者工具中重新上传云函数')
console.log('2. 或使用命令行工具: tcb functions:deploy <function-name>')
console.log('3. 确保在云开发控制台中开通了AI能力')
