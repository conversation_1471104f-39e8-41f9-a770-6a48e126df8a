const fs = require('fs')
const path = require('path')

console.log('🔧 开始更新云函数配置...')

const cloudfunctionsDir = path.join(__dirname, '../cloudfunctions')

// 云函数配置模板
const configTemplates = {
  checkText: {
    permissions: { openapi: [] },
    triggers: [],
    envVariables: {},
    runtime: "Nodejs16.13",
    memorySize: 512,
    timeout: 60,
    installDependency: true
  },
  checkImage: {
    permissions: { openapi: [] },
    triggers: [],
    envVariables: {},
    runtime: "Nodejs16.13",
    memorySize: 512,
    timeout: 60,
    installDependency: true
  },
  submitFeedback: {
    permissions: { openapi: [] },
    triggers: [],
    envVariables: {},
    runtime: "Nodejs16.13",
    memorySize: 256,
    timeout: 30,
    installDependency: true
  },
  getHistory: {
    permissions: { openapi: [] },
    triggers: [],
    envVariables: {},
    runtime: "Nodejs16.13",
    memorySize: 256,
    timeout: 30,
    installDependency: true
  },
  add: {
    permissions: { openapi: [] },
    triggers: [],
    envVariables: {},
    runtime: "Nodejs16.13",
    memorySize: 256,
    timeout: 30,
    installDependency: true
  }
}

// 检查云函数目录是否存在
if (!fs.existsSync(cloudfunctionsDir)) {
  console.error('❌ 云函数目录不存在:', cloudfunctionsDir)
  process.exit(1)
}

// 获取所有云函数目录
const cloudFunctions = fs.readdirSync(cloudfunctionsDir).filter(item => {
  const itemPath = path.join(cloudfunctionsDir, item)
  return fs.statSync(itemPath).isDirectory()
})

console.log('📁 发现云函数:', cloudFunctions.join(', '))

// 为每个云函数更新配置
cloudFunctions.forEach(functionName => {
  const functionDir = path.join(cloudfunctionsDir, functionName)
  const configPath = path.join(functionDir, 'config.json')
  
  console.log(`\n📝 更新云函数配置: ${functionName}`)
  
  try {
    // 获取配置模板
    const config = configTemplates[functionName] || configTemplates.add
    
    // 写入配置文件
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
    console.log(`   ✅ ${functionName} 配置更新完成`)
    
    // 验证package.json
    const packageJsonPath = path.join(functionDir, 'package.json')
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      if (packageJson.dependencies && packageJson.dependencies['wx-server-sdk']) {
        console.log(`   ✅ wx-server-sdk 依赖配置正确: ${packageJson.dependencies['wx-server-sdk']}`)
      } else {
        console.warn(`   ⚠️ ${functionName} 缺少wx-server-sdk依赖`)
      }
    } else {
      console.warn(`   ⚠️ ${functionName} 缺少package.json`)
    }
    
  } catch (error) {
    console.error(`   ❌ ${functionName} 配置更新失败:`, error.message)
  }
})

console.log('\n🎉 云函数配置更新完成!')
console.log('\n💡 接下来的步骤:')
console.log('1. 运行: npm run install:cloud-deps')
console.log('2. 在微信开发者工具中重新上传云函数')
console.log('3. 确保在云开发控制台中开通了AI能力')
console.log('4. 检查云函数的运行时环境是否为Node.js 16.13')
