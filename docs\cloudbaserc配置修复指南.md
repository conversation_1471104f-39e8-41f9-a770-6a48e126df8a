# cloudbaserc.json 配置修复指南

## 问题描述

原有的 `cloudbaserc.json` 配置过于复杂，包含了不必要的 framework 插件配置，导致云函数部署和运行失败。

## 解决方案

### 1. 简化配置结构

已将复杂的配置简化为基础结构：

**修复前（复杂配置）**:
```json
{
  "envId": "cloud1-4gqrn7kua46d9633",
  "framework": {
    "name": "uni-app",
    "plugins": {
      "client": { ... },
      "server": { ... }
    }
  },
  "region": "ap-shanghai",
  "$schema": "..."
}
```

**修复后（简化配置）**:
```json
{
  "envId": "cloud1-4gqrn7kua46d9633",
  "functions": [
    {
      "name": "add",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    },
    {
      "name": "checkText",
      "timeout": 60,
      "memorySize": 512,
      "installDependency": true
    },
    {
      "name": "checkImage",
      "timeout": 60,
      "memorySize": 512,
      "installDependency": true
    },
    {
      "name": "submitFeedback",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    },
    {
      "name": "getHistory",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    }
  ]
}
```

### 2. 配置字段说明

#### 必需字段
- **envId**: 云开发环境ID，从云开发控制台获取
- **functions**: 云函数配置数组

#### 云函数配置字段
- **name**: 云函数名称，必须与 `cloudfunctions` 目录下的文件夹名一致
- **timeout**: 超时时间（秒）
  - 简单函数（add, submitFeedback, getHistory）: 30秒
  - 复杂函数（checkText, checkImage）: 60秒
- **memorySize**: 内存大小（MB）
  - 简单函数: 256MB
  - AI相关函数: 512MB
- **installDependency**: 是否自动安装依赖，建议设为 `true`

### 3. 验证配置

运行验证脚本检查配置是否正确：

```bash
npm run validate:cloudbaserc
```

验证内容包括：
- JSON 格式是否正确
- 必需字段是否存在
- 字段类型是否正确
- 云函数配置是否完整

### 4. 部署流程

#### 4.1 验证和构建
```bash
# 1. 验证配置文件
npm run validate:cloudbaserc

# 2. 检查云函数状态
npm run check:cloud-functions

# 3. 构建项目
npm run build:mp-weixin
```

#### 4.2 部署云函数
1. 在微信开发者工具中打开构建目录
2. 确认 `cloudbaserc.json` 文件存在
3. 右键点击每个云函数目录
4. 选择"上传并部署：云端安装依赖"

### 5. 常见问题解决

#### Q1: 配置文件格式错误
**症状**: JSON 解析失败
**解决**: 
1. 检查 JSON 格式（括号、逗号、引号）
2. 使用 JSON 验证工具检查
3. 运行 `npm run validate:cloudbaserc` 验证

#### Q2: 云函数部署失败
**症状**: 上传时提示错误
**解决**:
1. 确认云函数名称与目录名一致
2. 检查 `installDependency` 设为 `true`
3. 确认网络连接正常

#### Q3: 云函数调用超时
**症状**: 函数执行超时
**解决**:
1. 增加 `timeout` 值
2. 优化函数代码逻辑
3. 检查 AI 模型调用是否正常

#### Q4: 内存不足错误
**症状**: 函数执行时内存溢出
**解决**:
1. 增加 `memorySize` 值
2. 优化代码内存使用
3. 检查是否有内存泄漏

### 6. 最佳实践

#### 6.1 配置管理
- 保持配置文件简洁
- 避免使用复杂的 framework 配置
- 定期验证配置文件格式

#### 6.2 版本控制
- 将 `cloudbaserc.json` 加入版本控制
- 记录每次配置变更
- 备份工作配置

#### 6.3 环境管理
- 为不同环境使用不同的 envId
- 使用环境变量管理敏感配置
- 建立开发/测试/生产环境

### 7. 配置模板

#### 基础模板
```json
{
  "envId": "你的环境ID",
  "functions": [
    {
      "name": "函数名",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    }
  ]
}
```

#### 完整模板（本项目）
```json
{
  "envId": "cloud1-4gqrn7kua46d9633",
  "functions": [
    {
      "name": "add",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    },
    {
      "name": "checkText",
      "timeout": 60,
      "memorySize": 512,
      "installDependency": true
    },
    {
      "name": "checkImage",
      "timeout": 60,
      "memorySize": 512,
      "installDependency": true
    },
    {
      "name": "submitFeedback",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    },
    {
      "name": "getHistory",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    }
  ]
}
```

### 8. 验证命令

```bash
# 验证配置文件
npm run validate:cloudbaserc

# 检查云函数状态
npm run check:cloud-functions

# 验证构建结果
npm run verify:build

# 强制重新部署准备
npm run force:deploy
```

### 9. 总结

通过简化 `cloudbaserc.json` 配置：

1. ✅ 移除了不必要的复杂配置
2. ✅ 保留了核心的云函数配置
3. ✅ 提供了清晰的配置结构
4. ✅ 添加了完整的验证机制

新的配置更加简洁、可靠，符合云开发的最佳实践。
