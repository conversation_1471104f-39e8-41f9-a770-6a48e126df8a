const fs = require('fs')
const path = require('path')

console.log('🔍 验证 cloudbaserc.json 配置...')

const cloudbasercPath = path.join(__dirname, '../cloudbaserc.json')

// 检查文件是否存在
if (!fs.existsSync(cloudbasercPath)) {
  console.error('❌ cloudbaserc.json 文件不存在')
  console.log('💡 请在项目根目录创建 cloudbaserc.json 文件')
  process.exit(1)
}

try {
  // 读取并解析配置文件
  const configContent = fs.readFileSync(cloudbasercPath, 'utf8')
  const config = JSON.parse(configContent)
  
  console.log('✅ JSON 格式正确')
  
  // 验证必要字段
  let isValid = true
  
  // 检查 envId
  if (!config.envId) {
    console.error('❌ 缺少 envId 字段')
    isValid = false
  } else if (typeof config.envId !== 'string') {
    console.error('❌ envId 必须是字符串')
    isValid = false
  } else {
    console.log(`✅ envId: ${config.envId}`)
  }
  
  // 检查 functions
  if (!config.functions) {
    console.error('❌ 缺少 functions 字段')
    isValid = false
  } else if (!Array.isArray(config.functions)) {
    console.error('❌ functions 必须是数组')
    isValid = false
  } else {
    console.log(`✅ functions: ${config.functions.length} 个云函数`)
    
    // 验证每个云函数配置
    config.functions.forEach((func, index) => {
      console.log(`\n📦 云函数 ${index + 1}: ${func.name || '未命名'}`)
      
      // 检查必要字段
      if (!func.name) {
        console.error('   ❌ 缺少 name 字段')
        isValid = false
      } else {
        console.log(`   ✅ name: ${func.name}`)
      }
      
      if (typeof func.timeout !== 'number') {
        console.error('   ❌ timeout 必须是数字')
        isValid = false
      } else {
        console.log(`   ✅ timeout: ${func.timeout}s`)
      }
      
      if (typeof func.memorySize !== 'number') {
        console.error('   ❌ memorySize 必须是数字')
        isValid = false
      } else {
        console.log(`   ✅ memorySize: ${func.memorySize}MB`)
      }
      
      if (typeof func.installDependency !== 'boolean') {
        console.error('   ❌ installDependency 必须是布尔值')
        isValid = false
      } else {
        console.log(`   ✅ installDependency: ${func.installDependency}`)
      }
    })
  }
  
  // 检查是否有不必要的复杂配置
  const unnecessaryFields = ['framework', 'plugins', 'region', '$schema']
  unnecessaryFields.forEach(field => {
    if (config[field]) {
      console.warn(`⚠️ 发现不必要的字段: ${field}`)
      console.log('   💡 建议移除以简化配置')
    }
  })
  
  // 输出验证结果
  console.log('\n' + '='.repeat(50))
  if (isValid) {
    console.log('🎉 cloudbaserc.json 配置验证通过!')
    console.log('✅ 配置格式正确')
    console.log('✅ 所有必要字段都存在')
    console.log('✅ 字段类型正确')
    
    console.log('\n💡 下一步操作:')
    console.log('1. 运行构建命令: npm run build:mp-weixin')
    console.log('2. 在微信开发者工具中部署云函数')
    console.log('3. 测试云函数调用')
  } else {
    console.log('❌ cloudbaserc.json 配置验证失败!')
    console.log('请修复上述错误后重新验证')
    process.exit(1)
  }
  
} catch (error) {
  console.error('❌ 配置文件解析失败:', error.message)
  
  if (error instanceof SyntaxError) {
    console.log('\n💡 JSON 格式错误修复建议:')
    console.log('1. 检查是否有多余的逗号')
    console.log('2. 检查括号是否匹配')
    console.log('3. 检查字符串是否用双引号包围')
    console.log('4. 使用 JSON 验证工具检查格式')
  }
  
  process.exit(1)
}

// 显示推荐的配置模板
console.log('\n📋 推荐的配置模板:')
console.log(`{
  "envId": "你的环境ID",
  "functions": [
    {
      "name": "add",
      "timeout": 30,
      "memorySize": 256,
      "installDependency": true
    },
    {
      "name": "checkText",
      "timeout": 60,
      "memorySize": 512,
      "installDependency": true
    }
  ]
}`)

console.log('\n📖 配置说明:')
console.log('- envId: 云开发环境ID，从控制台获取')
console.log('- functions: 云函数配置数组')
console.log('- name: 云函数名称，必须与目录名一致')
console.log('- timeout: 超时时间（秒），建议 30-60')
console.log('- memorySize: 内存大小（MB），建议 256-512')
console.log('- installDependency: 是否自动安装依赖，建议 true')
