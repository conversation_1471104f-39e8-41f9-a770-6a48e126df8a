# CloudBase AI SDK 使用指南

## 概述

根据腾讯云开发官方文档，我们已将云函数中的AI调用升级为使用CloudBase AI SDK，以获得更好的性能、稳定性和功能支持。

## 主要变更

### 1. 依赖更新

**之前（使用微信小程序基础库）**:
```javascript
const model = cloud.extend.AI.createModel("deepseek")
```

**现在（使用CloudBase AI SDK）**:
```javascript
const cloudbase = require('@cloudbase/js-sdk')
const adapter = require('@cloudbase/adapter-node')

// 初始化
const app = cloudbase.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const auth = app.auth({ storage: sessionStorage })
await auth.signInAnonymously()
const ai = await app.ai()
const model = ai.createModel("deepseek")
```

### 2. 新增依赖包

每个AI云函数的 `package.json` 现在包含：
```json
{
  "dependencies": {
    "wx-server-sdk": "~3.0.1",
    "@cloudbase/js-sdk": "^1.7.7",
    "@cloudbase/adapter-node": "^1.0.0"
  }
}
```

### 3. API调用方式变更

**之前**:
```javascript
const aiResponse = await model.streamText({
  data: {
    model: "deepseek-r1",
    messages: [...],
    temperature: 0.3
  }
})
```

**现在**:
```javascript
const aiResponse = await model.streamText({
  model: "deepseek-r1",
  messages: [...],
  temperature: 0.3
})
```

## 受影响的云函数

### 1. checkText 云函数
- **功能**: 文本信息核查
- **AI模型**: DeepSeek
- **更新内容**: 
  - 使用CloudBase AI SDK初始化
  - 优化AI调用参数
  - 改进错误处理

### 2. checkImage 云函数
- **功能**: 图片信息核查（OCR + AI分析）
- **AI模型**: DeepSeek
- **更新内容**:
  - 使用CloudBase AI SDK初始化
  - 优化图片文本分析流程
  - 改进错误处理

## CloudBase AI SDK 优势

### 1. 统一的API接口
- 跨平台一致的使用体验
- 标准化的参数格式
- 更好的类型支持

### 2. 更好的性能
- 优化的网络请求
- 更高效的流式处理
- 减少延迟

### 3. 增强的功能
- 支持更多AI模型
- 工具调用支持
- Agent集成能力

### 4. 改进的错误处理
- 详细的错误信息
- 自动重试机制
- 更好的调试支持

## 部署步骤

### 1. 更新依赖
```bash
# 更新AI SDK依赖
npm run update:ai-sdk

# 安装云函数依赖
npm run install:cloud-deps
```

### 2. 重新部署云函数
1. 在微信开发者工具中右键每个云函数目录
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 3. 验证功能
```bash
# 构建项目
npm run build:mp-weixin

# 验证构建结果
npm run verify:build

# 检查云函数状态
npm run check:cloud-functions
```

## 使用示例

### 文本核查示例
```javascript
// 在checkText云函数中
const ai = await app.ai()
const model = ai.createModel("deepseek")

const response = await model.streamText({
  model: "deepseek-r1",
  messages: [
    { role: "system", content: "你是专业的信息核查助手..." },
    { role: "user", content: "请核查以下信息..." }
  ],
  temperature: 0.3,
  max_tokens: 1500
})

// 处理流式响应
let result = ''
for await (let chunk of response.textStream) {
  result += chunk
}
```

### 图片核查示例
```javascript
// 在checkImage云函数中
const ai = await app.ai()
const model = ai.createModel("deepseek")

const response = await model.streamText({
  model: "deepseek-r1",
  messages: [
    { role: "system", content: "分析OCR提取的文本..." },
    { role: "user", content: `OCR结果: ${ocrText}` }
  ],
  temperature: 0.3
})
```

## 配置说明

### 1. 环境配置
- 自动使用当前云开发环境
- 支持匿名登录认证
- 使用Node.js适配器

### 2. 模型配置
- 默认使用DeepSeek模型
- 支持流式响应
- 可配置温度参数

### 3. 错误处理
- 自动捕获AI调用错误
- 提供降级处理方案
- 详细的错误日志

## 故障排除

### 1. 依赖安装失败
```bash
# 清理并重新安装
npm run install:cloud-deps
```

### 2. AI调用失败
- 检查云开发环境是否正确
- 确认AI能力已开通
- 查看云函数执行日志

### 3. 认证失败
- 确认CloudBase环境配置正确
- 检查匿名登录是否启用
- 验证适配器配置

## 性能优化

### 1. 初始化优化
- 复用AI实例
- 缓存认证状态
- 减少重复初始化

### 2. 响应优化
- 使用流式处理
- 合理设置超时时间
- 优化提示词长度

### 3. 错误处理优化
- 快速失败机制
- 智能重试策略
- 降级处理方案

## 监控和调试

### 1. 日志记录
- AI调用详细日志
- 错误堆栈信息
- 性能指标记录

### 2. 调试工具
- 使用调试页面测试
- 云函数日志查看
- 性能分析工具

### 3. 监控指标
- AI调用成功率
- 响应时间统计
- 错误率监控

## 最佳实践

### 1. 代码组织
- 统一的AI初始化模式
- 标准化的错误处理
- 可复用的工具函数

### 2. 性能优化
- 合理的超时设置
- 适当的并发控制
- 资源使用监控

### 3. 安全考虑
- 输入参数验证
- 输出内容过滤
- 访问权限控制

## 总结

通过升级到CloudBase AI SDK，我们获得了：

1. ✅ 更稳定的AI服务调用
2. ✅ 更好的性能表现
3. ✅ 更丰富的功能支持
4. ✅ 更标准化的API接口
5. ✅ 更完善的错误处理

这次升级为项目的AI功能提供了更坚实的技术基础，为后续功能扩展奠定了良好基础。
