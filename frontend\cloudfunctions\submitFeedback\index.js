// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 获取数据库引用
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数submitFeedback被调用，事件参数:', event)
  console.log('上下文信息:', context)
  
  try {
    const wxContext = cloud.getWXContext()
    console.log('用户信息:', {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    })

    // 获取请求参数
    const { check_id, rating, comment, user_id } = event
    
    // 参数验证
    if (!check_id) {
      return {
        code: 400,
        message: '核查ID不能为空',
        error: 'check_id parameter is required',
        timestamp: new Date().toISOString()
      }
    }

    if (!rating || !['accurate', 'inaccurate'].includes(rating)) {
      return {
        code: 400,
        message: '评价类型无效，必须是 accurate 或 inaccurate',
        error: 'rating parameter must be accurate or inaccurate',
        timestamp: new Date().toISOString()
      }
    }

    // 生成反馈记录ID
    const feedbackId = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const submittedAt = new Date().toISOString()
    
    console.log('开始处理用户反馈，反馈ID:', feedbackId)

    // 构建反馈数据
    const feedbackData = {
      feedback_id: feedbackId,
      check_id: check_id,
      rating: rating,
      comment: comment || '',
      user_id: user_id || wxContext.OPENID,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      submitted_at: submittedAt,
      created_time: db.serverDate(),
      ip_address: context.CLIENTIP || 'unknown',
      user_agent: context.CLIENTUA || 'unknown'
    }

    // 保存反馈到数据库
    try {
      const addResult = await db.collection('user_feedback').add({
        data: feedbackData
      })
      
      console.log('反馈保存成功，数据库ID:', addResult._id)
      
      // 可以在这里添加额外的处理逻辑，比如：
      // 1. 发送通知给管理员
      // 2. 更新核查结果的统计信息
      // 3. 触发模型优化流程等
      
      // 统计该核查结果的反馈情况
      const feedbackStats = await db.collection('user_feedback')
        .where({
          check_id: check_id
        })
        .get()
      
      const totalFeedbacks = feedbackStats.data.length
      const accurateFeedbacks = feedbackStats.data.filter(f => f.rating === 'accurate').length
      const inaccurateFeedbacks = feedbackStats.data.filter(f => f.rating === 'inaccurate').length
      
      console.log(`核查结果 ${check_id} 的反馈统计:`, {
        total: totalFeedbacks,
        accurate: accurateFeedbacks,
        inaccurate: inaccurateFeedbacks,
        accuracy_rate: totalFeedbacks > 0 ? (accurateFeedbacks / totalFeedbacks * 100).toFixed(2) + '%' : '0%'
      })

      // 如果是负面反馈，可以记录详细信息用于改进
      if (rating === 'inaccurate') {
        console.log('收到负面反馈，需要关注:', {
          check_id: check_id,
          comment: comment,
          user_id: user_id || wxContext.OPENID
        })
        
        // 这里可以添加告警逻辑，比如发送邮件或消息通知
      }

      return {
        code: 200,
        message: '反馈提交成功',
        data: {
          feedback_id: feedbackId,
          submitted_at: submittedAt,
          stats: {
            total_feedbacks: totalFeedbacks,
            accurate_count: accurateFeedbacks,
            inaccurate_count: inaccurateFeedbacks
          }
        },
        timestamp: new Date().toISOString()
      }
      
    } catch (dbError) {
      console.error('数据库操作失败:', dbError)
      
      // 如果数据库操作失败，仍然返回成功，避免影响用户体验
      // 但记录错误日志用于后续处理
      return {
        code: 200,
        message: '反馈已收到，感谢您的参与',
        data: {
          feedback_id: feedbackId,
          submitted_at: submittedAt
        },
        timestamp: new Date().toISOString(),
        warning: '数据存储异常，已记录日志'
      }
    }
    
  } catch (error) {
    console.error('反馈提交失败:', error)
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}
