# 云函数依赖错误修复指南

## 错误描述

云函数执行时报错：
```
Error: Cannot find module 'wx-server-sdk'
errCode: -504002 functions execute fail
```

## 错误原因分析

1. **缺少cloudbaserc.json配置文件** - 云开发环境配置不完整
2. **云函数依赖未安装** - wx-server-sdk模块没有正确安装到云端
3. **配置文件不完整** - config.json缺少必要的运行时配置
4. **部署方式问题** - 依赖没有随云函数一起上传

## 解决方案

### 步骤1: 创建cloudbaserc.json配置

已创建`cloudbaserc.json`文件，包含：
- 环境ID配置
- 云函数运行时配置
- 依赖安装配置

### 步骤2: 更新云函数配置

运行配置更新脚本：
```bash
npm run update:cloud-configs
```

这将为所有云函数创建正确的config.json配置，包括：
- 运行时环境: Nodejs16.13
- 内存配置: 256MB-512MB
- 超时时间: 30-60秒
- 自动安装依赖: true

### 步骤3: 安装云函数依赖

运行依赖安装脚本：
```bash
npm run install:cloud-deps
```

这将：
- 清理旧的node_modules
- 为每个云函数安装wx-server-sdk
- 验证依赖安装结果

### 步骤4: 重新部署云函数

#### 方法一: 微信开发者工具部署
1. 在微信开发者工具中打开项目
2. 右键点击每个云函数目录
3. 选择"上传并部署：云端安装依赖"

#### 方法二: 命令行部署
```bash
# 安装云开发CLI工具
npm install -g @cloudbase/cli

# 登录
tcb login

# 部署单个云函数
tcb functions:deploy checkText
tcb functions:deploy checkImage
tcb functions:deploy submitFeedback
tcb functions:deploy getHistory

# 或批量部署
tcb functions:deploy
```

## 配置文件说明

### cloudbaserc.json
```json
{
  "envId": "cloud1-4gqrn7kua46d9633",
  "framework": {
    "plugins": {
      "server": {
        "use": "@cloudbase/framework-plugin-function",
        "inputs": {
          "functionRootPath": "cloudfunctions",
          "functions": [
            {
              "name": "checkText",
              "config": {
                "runtime": "Nodejs16.13",
                "memorySize": 512,
                "timeout": 60,
                "installDependency": true
              }
            }
          ]
        }
      }
    }
  }
}
```

### config.json (每个云函数)
```json
{
  "permissions": {
    "openapi": []
  },
  "runtime": "Nodejs16.13",
  "memorySize": 512,
  "timeout": 60,
  "installDependency": true
}
```

### package.json (每个云函数)
```json
{
  "name": "check-text",
  "dependencies": {
    "wx-server-sdk": "~3.0.1"
  }
}
```

## 验证修复结果

### 1. 本地验证
```bash
# 检查依赖是否安装
ls cloudfunctions/checkText/node_modules/wx-server-sdk

# 检查配置文件
cat cloudfunctions/checkText/config.json
```

### 2. 云端验证
在微信开发者工具中：
1. 打开云开发控制台
2. 查看云函数列表
3. 检查运行时环境是否为Node.js 16.13
4. 测试云函数调用

### 3. 功能验证
在小程序中测试：
1. 文本核查功能
2. 图片核查功能
3. 用户反馈功能
4. 历史记录功能

## 常见问题解决

### Q1: 依赖安装后仍然报错
**解决方案**:
1. 检查云函数运行时环境是否为Node.js 16.13
2. 确保在云开发控制台中重新部署了云函数
3. 检查wx-server-sdk版本是否兼容

### Q2: AI模型调用失败
**解决方案**:
1. 在云开发控制台开通AI能力
2. 确保小程序基础库版本 >= 3.7.1
3. 检查DeepSeek模型访问权限

### Q3: 云函数超时
**解决方案**:
1. 增加云函数超时时间配置
2. 优化AI模型调用参数
3. 检查网络连接状况

### Q4: 内存不足错误
**解决方案**:
1. 增加云函数内存配置
2. 优化代码逻辑，减少内存使用
3. 检查是否有内存泄漏

## 预防措施

### 1. 版本管理
- 固定wx-server-sdk版本号
- 定期更新依赖版本
- 测试新版本兼容性

### 2. 配置管理
- 使用版本控制管理配置文件
- 建立配置文件模板
- 定期备份云函数配置

### 3. 部署流程
- 建立标准化部署流程
- 使用自动化部署脚本
- 部署前进行本地测试

### 4. 监控告警
- 设置云函数错误告警
- 监控云函数执行时间
- 定期检查依赖安全性

## 完整修复流程

```bash
# 1. 更新云函数配置
npm run update:cloud-configs

# 2. 安装云函数依赖
npm run install:cloud-deps

# 3. 构建项目
npm run build:mp-weixin

# 4. 在微信开发者工具中重新部署云函数

# 5. 测试功能
```

## 总结

通过以上步骤，我们解决了：
1. ✅ 缺少cloudbaserc.json配置
2. ✅ wx-server-sdk依赖未安装
3. ✅ 云函数配置不完整
4. ✅ 部署流程不规范

现在云函数应该能够正常执行，不再出现模块找不到的错误。
