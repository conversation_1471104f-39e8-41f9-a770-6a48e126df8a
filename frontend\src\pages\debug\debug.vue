<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">云函数调试工具</text>
			
			<view class="debug-section">
				<text class="section-title">环境信息</text>
				<view class="info-item">
					<text>云开发环境ID: {{ envId }}</text>
				</view>
				<view class="info-item">
					<text>初始化状态: {{ initStatus }}</text>
				</view>
			</view>
			
			<view class="debug-section">
				<text class="section-title">云函数测试</text>
				
				<button 
					@click="testAddFunction"
					class="test-btn"
					:disabled="testing"
				>
					{{ testing ? '测试中...' : '测试 add 云函数' }}
				</button>
				
				<button 
					@click="testCheckTextFunction"
					class="test-btn"
					:disabled="testing"
				>
					{{ testing ? '测试中...' : '测试 checkText 云函数' }}
				</button>
				
				<button 
					@click="listCloudFunctions"
					class="test-btn"
					:disabled="testing"
				>
					{{ testing ? '查询中...' : '查询云函数列表' }}
				</button>
			</view>
			
			<view class="debug-section" v-if="testResults.length > 0">
				<text class="section-title">测试结果</text>
				<view 
					v-for="(result, index) in testResults" 
					:key="index"
					class="result-item"
					:class="result.success ? 'success' : 'error'"
				>
					<view class="result-header">
						<text class="result-title">{{ result.title }}</text>
						<text class="result-status">{{ result.success ? '成功' : '失败' }}</text>
					</view>
					<view class="result-content">
						<text>{{ result.message }}</text>
					</view>
					<view class="result-detail" v-if="result.detail">
						<text class="detail-text">{{ result.detail }}</text>
					</view>
				</view>
			</view>
			
			<view class="debug-section">
				<button 
					@click="clearResults"
					class="clear-btn"
				>
					清空结果
				</button>
			</view>
		</uni-card>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'

declare const wx: {
	cloud: {
		init: (options: any) => void
		callFunction: (options: any) => Promise<any>
		DYNAMIC_CURRENT_ENV: string
	}
}

interface TestResult {
	title: string
	success: boolean
	message: string
	detail?: string
}

export default defineComponent({
	name: 'Debug',
	setup() {
		const envId = ref('cloud1-4gqrn7kua46d9633')
		const initStatus = ref('未初始化')
		const testing = ref(false)
		const testResults = ref<TestResult[]>([])
		
		// 初始化云开发
		onMounted(() => {
			try {
				wx.cloud.init({
					env: envId.value,
					traceUser: true
				})
				initStatus.value = '已初始化'
				addResult('云开发初始化', true, '云开发环境初始化成功')
			} catch (error) {
				initStatus.value = '初始化失败'
				addResult('云开发初始化', false, '云开发环境初始化失败', error.message)
			}
		})
		
		// 添加测试结果
		const addResult = (title: string, success: boolean, message: string, detail?: string) => {
			testResults.value.unshift({
				title,
				success,
				message,
				detail
			})
		}
		
		// 测试add云函数
		const testAddFunction = async () => {
			testing.value = true
			try {
				const res = await wx.cloud.callFunction({
					name: 'add',
					data: {
						a: 1,
						b: 2
					}
				})
				
				console.log('add云函数调用成功:', res)
				addResult(
					'add云函数测试', 
					true, 
					`计算结果: ${res.result.sum}`,
					JSON.stringify(res, null, 2)
				)
			} catch (error) {
				console.error('add云函数调用失败:', error)
				addResult(
					'add云函数测试', 
					false, 
					`调用失败: ${error.message}`,
					JSON.stringify(error, null, 2)
				)
			} finally {
				testing.value = false
			}
		}
		
		// 测试checkText云函数
		const testCheckTextFunction = async () => {
			testing.value = true
			try {
				const res = await wx.cloud.callFunction({
					name: 'checkText',
					data: {
						text: '这是一个测试文本',
						user_id: 'debug_user',
						session_id: 'debug_session'
					}
				})
				
				console.log('checkText云函数调用成功:', res)
				addResult(
					'checkText云函数测试', 
					true, 
					'文本核查功能正常',
					JSON.stringify(res.result, null, 2)
				)
			} catch (error) {
				console.error('checkText云函数调用失败:', error)
				addResult(
					'checkText云函数测试', 
					false, 
					`调用失败: ${error.message}`,
					JSON.stringify(error, null, 2)
				)
			} finally {
				testing.value = false
			}
		}
		
		// 查询云函数列表
		const listCloudFunctions = async () => {
			testing.value = true
			try {
				// 尝试调用每个云函数来检查是否存在
				const functions = ['add', 'checkText', 'checkImage', 'submitFeedback', 'getHistory']
				const results = []
				
				for (const funcName of functions) {
					try {
						await wx.cloud.callFunction({
							name: funcName,
							data: { test: true }
						})
						results.push(`${funcName}: 存在`)
					} catch (error) {
						if (error.message.includes('404') || error.message.includes('not found')) {
							results.push(`${funcName}: 不存在`)
						} else {
							results.push(`${funcName}: 存在但有错误`)
						}
					}
				}
				
				addResult(
					'云函数列表查询', 
					true, 
					'查询完成',
					results.join('\n')
				)
			} catch (error) {
				addResult(
					'云函数列表查询', 
					false, 
					`查询失败: ${error.message}`,
					JSON.stringify(error, null, 2)
				)
			} finally {
				testing.value = false
			}
		}
		
		// 清空结果
		const clearResults = () => {
			testResults.value = []
		}
		
		return {
			envId,
			initStatus,
			testing,
			testResults,
			testAddFunction,
			testCheckTextFunction,
			listCloudFunctions,
			clearResults
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.debug-section {
		margin: 30rpx 0;
		
		.section-title {
			display: block;
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 20rpx;
		}
		
		.info-item {
			margin: 10rpx 0;
			padding: 10rpx;
			background-color: #F8F9FA;
			border-radius: 8rpx;
		}
	}
	
	.test-btn {
		margin: 10rpx 0;
		background-color: #007AFF;
		color: #FFFFFF;
		
		&:active {
			background-color: #0056CC;
		}
		
		&:disabled {
			background-color: #CCCCCC;
		}
	}
	
	.clear-btn {
		background-color: #FA5151;
		color: #FFFFFF;
		
		&:active {
			background-color: #E04444;
		}
	}
	
	.result-item {
		margin: 15rpx 0;
		padding: 20rpx;
		border-radius: 8rpx;
		border-left: 4rpx solid;
		
		&.success {
			background-color: #F0F9FF;
			border-left-color: #07C160;
		}
		
		&.error {
			background-color: #FFF5F5;
			border-left-color: #FA5151;
		}
		
		.result-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10rpx;
			
			.result-title {
				font-weight: bold;
				color: #333333;
			}
			
			.result-status {
				font-size: 24rpx;
				padding: 4rpx 8rpx;
				border-radius: 4rpx;
				color: #FFFFFF;
			}
		}
		
		&.success .result-status {
			background-color: #07C160;
		}
		
		&.error .result-status {
			background-color: #FA5151;
		}
		
		.result-content {
			margin: 10rpx 0;
			color: #666666;
		}
		
		.result-detail {
			margin-top: 10rpx;
			padding: 10rpx;
			background-color: #F5F5F5;
			border-radius: 4rpx;
			
			.detail-text {
				font-size: 24rpx;
				color: #999999;
				word-break: break-all;
			}
		}
	}
}
</style>
