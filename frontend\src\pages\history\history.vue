<template>
	<view class="container">
		<uni-card v-if="!historyList || historyList.length === 0" :is-shadow="false" is-full>
			<view class="empty-state">
				<uni-icons type="info" size="64" color="#999999" />
				<text class="empty-text">暂无核查记录</text>
			</view>
		</uni-card>
		
		<template v-else-if="historyList && historyList.length > 0">
			<uni-swipe-action>
				<uni-swipe-action-item
					v-for="(item, index) in historyList"
					:key="index"
					:right-options="swipeOptions"
					@click="handleSwipeClick($event, item)"
				>
					<uni-card :is-shadow="false" is-full>
						<view class="history-item" @click="viewDetail(item)">
							<!-- 状态标签 -->
							<view class="status-tag" :class="getStatusClass(item.status)">
								<text class="status-text">{{ getStatusText(item.status) }}</text>
							</view>
							
							<!-- 内容摘要 -->
							<view class="content-summary">
								<text class="summary-text">{{ item.content }}</text>
							</view>
							
							<!-- 时间信息 -->
							<view class="time-info">
								<text class="check-time">{{ formatTime(item.checkTime) }}</text>
							</view>
						</view>
					</uni-card>
				</uni-swipe-action-item>
			</uni-swipe-action>
			
			<!-- 清空历史按钮 -->
			<view class="clear-history">
				<button 
					type="warn" 
					size="mini" 
					@click="showClearConfirm"
				>清空历史记录</button>
			</view>
		</template>
		
		<!-- 底部提示 -->
		<view class="bottom-tip" v-if="historyList && historyList.length > 0">
			<text class="tip-text">已显示全部历史记录</text>
		</view>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'

// 历史记录项类型定义
interface HistoryItem {
	id: string
	content: string
	status: 'supported' | 'disputed' | 'insufficient'
	checkTime: number
	result?: any
}

export default defineComponent({
	name: 'History',
	// 下拉刷新处理
	onPullDownRefresh() {
		this.loadHistory()
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 500)
	},
	setup() {
		// 历史记录列表
		const historyList = ref<HistoryItem[]>([])

		
		// 滑动操作选项
		const swipeOptions = [
			{
				text: '删除',
				style: {
					backgroundColor: '#FA5151'
				}
			}
		]
		
		// 获取历史记录
		const loadHistory = () => {
			try {
				const storageHistory = uni.getStorageSync('checkHistory')
				if (storageHistory) {
					const parsed = JSON.parse(storageHistory)
					historyList.value = Array.isArray(parsed) ? parsed : []
				} else {
					historyList.value = []
				}
			} catch (error) {
				console.error('加载历史记录失败:', error)
				historyList.value = [] // 确保即使出错也有一个空数组
				uni.showToast({
					title: '加载历史记录失败',
					icon: 'none'
				})
			}
		}
		
		// 保存历史记录
		const saveHistory = () => {
			try {
				uni.setStorageSync('checkHistory', JSON.stringify(historyList.value))
			} catch (error) {
				console.error('保存历史记录失败:', error)
				uni.showToast({
					title: '保存历史记录失败',
					icon: 'none'
				})
			}
		}
		
		// 删除单条历史记录
		const deleteHistory = (item: HistoryItem) => {
			const index = historyList.value.findIndex(h => h.id === item.id)
			if (index > -1) {
				historyList.value.splice(index, 1)
				saveHistory()
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
			}
		}
		
		// 清空历史记录
		const clearHistory = () => {
			historyList.value = []
			saveHistory()
			uni.showToast({
				title: '已清空历史记录',
				icon: 'success'
			})
		}
		
		// 显示清空确认对话框
		const showClearConfirm = () => {
			uni.showModal({
				title: '确认清空',
				content: '是否清空所有历史记录？此操作不可恢复。',
				confirmText: '清空',
				confirmColor: '#FA5151',
				success: (res) => {
					if (res.confirm) {
						clearHistory()
					}
				}
			})
		}
		
		// 查看详情
		const viewDetail = (item: HistoryItem) => {
			// 将结果数据传递到结果页面
			uni.navigateTo({
				url: '/pages/result/result',
				success: (res) => {
					// 传递数据给结果页面
					if (res.eventChannel) {
						res.eventChannel.emit('checkResult', item.result)
					}
				}
			})
		}
		
		// 处理滑动操作点击
		const handleSwipeClick = (e: {content: {text: string}}, item: HistoryItem) => {
			if (e.content.text === '删除') {
				deleteHistory(item)
			}
		}
		
		// 获取状态样式类
		const getStatusClass = (status: string) => {
			const classMap = {
				supported: 'status-supported',
				disputed: 'status-disputed',
				insufficient: 'status-insufficient'
			}
			return classMap[status] || ''
		}
		
		// 获取状态文本
		const getStatusText = (status: string) => {
			const textMap = {
				supported: '信息可信',
				disputed: '信息存疑',
				insufficient: '信息不足'
			}
			return textMap[status] || ''
		}
		
		// 格式化时间
		const formatTime = (timestamp: number) => {
			const date = new Date(timestamp)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
		}
		
		// 页面加载时获取历史记录
		onMounted(() => {
			loadHistory()
		})
		
		return {
			historyList,
			swipeOptions,
			loadHistory,
			handleSwipeClick,
			viewDetail,
			getStatusClass,
			getStatusText,
			formatTime,
			showClearConfirm
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 0;
		
		.empty-text {
			margin-top: 20rpx;
			color: #999999;
			font-size: 28rpx;
		}
	}
	
	.history-item {
		.status-tag {
			display: inline-block;
			padding: 4rpx 12rpx;
			border-radius: 4rpx;
			margin-bottom: 16rpx;
			
			.status-text {
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}
		
		.status-supported {
			background-color: #07C160;
		}
		
		.status-disputed {
			background-color: #FA5151;
		}
		
		.status-insufficient {
			background-color: #FFC300;
		}
		
		.content-summary {
			margin: 16rpx 0;
			
			.summary-text {
				font-size: 28rpx;
				color: #333333;
				line-height: 1.6;
				
				// 最多显示两行
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
		}
		
		.time-info {
			.check-time {
				font-size: 24rpx;
				color: #999999;
			}
		}
	}
	
	.clear-history {
		margin: 40rpx 0;
		text-align: center;
	}

	.bottom-tip {
		padding: 30rpx 0;
		text-align: center;

		.tip-text {
			font-size: 24rpx;
			color: #999999;
		}
	}
}
</style> 