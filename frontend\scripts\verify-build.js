const fs = require('fs')
const path = require('path')

console.log('🔍 验证构建结果...')

const buildDir = path.join(__dirname, '../dist/build/mp-weixin')

// 检查构建目录是否存在
if (!fs.existsSync(buildDir)) {
  console.error('❌ 构建目录不存在:', buildDir)
  console.log('💡 请先运行构建命令: npm run build:mp-weixin')
  process.exit(1)
}

console.log('📂 构建目录:', buildDir)

// 必需文件列表
const requiredFiles = [
  { file: 'app.js', description: '应用入口文件' },
  { file: 'app.json', description: '应用配置文件' },
  { file: 'app.wxss', description: '应用样式文件' },
  { file: 'project.config.json', description: '项目配置文件' },
  { file: 'cloudbaserc.json', description: '云开发配置文件' }
]

// 可选文件列表
const optionalFiles = [
  { file: 'package.json', description: '包配置文件' },
  { file: '.gitignore', description: 'Git忽略文件' }
]

// 必需目录列表
const requiredDirs = [
  { dir: 'pages', description: '页面目录' },
  { dir: 'cloudfunctions', description: '云函数目录' }
]

let allPassed = true

// 检查必需文件
console.log('\n📄 检查必需文件:')
requiredFiles.forEach(({ file, description }) => {
  const filePath = path.join(buildDir, file)
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath)
    console.log(`✅ ${file}: ${description} (${stats.size} bytes)`)
    
    // 验证JSON文件格式
    if (file.endsWith('.json')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8')
        const parsed = JSON.parse(content)
        console.log(`   📝 JSON格式正确`)
        
        // 特殊验证
        if (file === 'project.config.json') {
          if (parsed.appid) {
            console.log(`   🆔 AppID: ${parsed.appid}`)
          } else {
            console.warn(`   ⚠️ 缺少AppID配置`)
          }
          
          if (parsed.cloudfunctionRoot) {
            console.log(`   ☁️ 云函数根目录: ${parsed.cloudfunctionRoot}`)
          } else {
            console.warn(`   ⚠️ 缺少云函数根目录配置`)
          }
        }
        
        if (file === 'cloudbaserc.json') {
          if (parsed.envId) {
            console.log(`   🌍 环境ID: ${parsed.envId}`)
          } else {
            console.error(`   ❌ 缺少环境ID配置`)
            allPassed = false
          }
        }
        
      } catch (error) {
        console.error(`   ❌ JSON格式错误: ${error.message}`)
        allPassed = false
      }
    }
  } else {
    console.error(`❌ ${file}: ${description} - 文件不存在`)
    allPassed = false
  }
})

// 检查可选文件
console.log('\n📄 检查可选文件:')
optionalFiles.forEach(({ file, description }) => {
  const filePath = path.join(buildDir, file)
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath)
    console.log(`✅ ${file}: ${description} (${stats.size} bytes)`)
  } else {
    console.log(`⚪ ${file}: ${description} - 文件不存在（可选）`)
  }
})

// 检查必需目录
console.log('\n📁 检查必需目录:')
requiredDirs.forEach(({ dir, description }) => {
  const dirPath = path.join(buildDir, dir)
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    const files = fs.readdirSync(dirPath)
    console.log(`✅ ${dir}: ${description} (${files.length} 个项目)`)
    
    if (dir === 'cloudfunctions') {
      console.log(`   📦 云函数列表: ${files.join(', ')}`)
      
      // 检查每个云函数的结构
      files.forEach(funcName => {
        const funcDir = path.join(dirPath, funcName)
        if (fs.statSync(funcDir).isDirectory()) {
          const indexJs = path.join(funcDir, 'index.js')
          const packageJson = path.join(funcDir, 'package.json')
          const configJson = path.join(funcDir, 'config.json')
          
          let funcStatus = []
          if (fs.existsSync(indexJs)) funcStatus.push('index.js')
          if (fs.existsSync(packageJson)) funcStatus.push('package.json')
          if (fs.existsSync(configJson)) funcStatus.push('config.json')
          
          console.log(`     ${funcName}: [${funcStatus.join(', ')}]`)
        }
      })
    }
    
    if (dir === 'pages') {
      console.log(`   📄 页面列表: ${files.join(', ')}`)
    }
  } else {
    console.error(`❌ ${dir}: ${description} - 目录不存在`)
    allPassed = false
  }
})

// 检查文件大小
console.log('\n📊 文件大小统计:')
const calculateDirSize = (dirPath) => {
  let totalSize = 0
  const files = fs.readdirSync(dirPath)
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file)
    const stats = fs.statSync(filePath)
    
    if (stats.isDirectory()) {
      totalSize += calculateDirSize(filePath)
    } else {
      totalSize += stats.size
    }
  })
  
  return totalSize
}

const totalSize = calculateDirSize(buildDir)
const sizeInMB = (totalSize / 1024 / 1024).toFixed(2)
console.log(`📦 总大小: ${sizeInMB} MB`)

// 输出结果
console.log('\n' + '='.repeat(50))
if (allPassed) {
  console.log('🎉 构建验证通过!')
  console.log('✅ 所有必需文件和目录都存在')
  console.log('✅ 配置文件格式正确')
  console.log('✅ 云函数结构完整')
  
  console.log('\n💡 下一步操作:')
  console.log('1. 在微信开发者工具中打开构建目录')
  console.log('2. 检查云开发环境配置')
  console.log('3. 上传并部署云函数')
  console.log('4. 测试小程序功能')
  
} else {
  console.log('❌ 构建验证失败!')
  console.log('请检查上述错误并重新构建')
  
  console.log('\n🔧 修复建议:')
  console.log('1. 运行: npm run build:mp-weixin')
  console.log('2. 或运行: npm run copy:configs')
  console.log('3. 检查源文件是否存在')
  
  process.exit(1)
}

console.log(`\n📂 构建目录: ${buildDir}`)
