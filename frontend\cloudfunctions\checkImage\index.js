// 云函数入口文件
const cloud = require('wx-server-sdk')
const cloudbase = require('@cloudbase/js-sdk')
const adapter = require('@cloudbase/adapter-node')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 初始化CloudBase AI SDK
const { sessionStorage } = adapter.genAdapter()
cloudbase.useAdapters(adapter)

const app = cloudbase.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数checkImage被调用，事件参数:', event)
  console.log('上下文信息:', context)
  
  try {
    const wxContext = cloud.getWXContext()
    console.log('用户信息:', {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    })

    // 获取请求参数
    const { fileID, user_id, session_id } = event
    
    // 参数验证
    if (!fileID) {
      return {
        code: 400,
        message: '图片文件ID不能为空',
        error: 'fileID parameter is required',
        timestamp: new Date().toISOString()
      }
    }

    // 生成唯一的核查ID
    const checkId = `check_img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    console.log('开始图片核查，核查ID:', checkId)
    console.log('图片文件ID:', fileID)

    // 获取云存储实例
    const storage = cloud.storage()
    
    // 下载图片文件
    let imageBuffer
    try {
      const downloadResult = await storage.downloadFile({
        fileID: fileID
      })
      imageBuffer = downloadResult.fileContent
      console.log('图片下载成功，大小:', imageBuffer.length)
    } catch (downloadError) {
      console.error('图片下载失败:', downloadError)
      return {
        code: 400,
        message: '图片文件下载失败',
        error: downloadError.message,
        timestamp: new Date().toISOString()
      }
    }

    // 检查文件大小（5MB限制）
    if (imageBuffer.length > 5 * 1024 * 1024) {
      return {
        code: 413,
        message: '图片文件过大，请上传小于5MB的图片',
        error: 'Image file too large',
        timestamp: new Date().toISOString()
      }
    }

    // 模拟OCR处理（实际项目中应该调用腾讯云OCR API）
    // 这里我们模拟一个OCR结果
    const mockOCRResult = {
      extracted_text: "这是通过OCR识别出的文本内容示例。在实际项目中，这里应该是真实的OCR识别结果。",
      confidence: 0.95,
      regions: [
        {
          text: "这是通过OCR识别出的文本内容示例",
          bbox: [100, 100, 400, 150],
          confidence: 0.98
        },
        {
          text: "在实际项目中，这里应该是真实的OCR识别结果",
          bbox: [100, 160, 450, 200],
          confidence: 0.92
        }
      ]
    }

    console.log('OCR识别完成:', mockOCRResult)

    // 如果OCR没有识别出文本，返回相应提示
    if (!mockOCRResult.extracted_text || mockOCRResult.extracted_text.trim().length === 0) {
      return {
        code: 200,
        message: 'success',
        data: {
          check_id: checkId,
          ocr_result: {
            extracted_text: '',
            confidence: 0,
            regions: []
          },
          status: 'insufficient',
          summary: '图片中未识别出可分析的文本内容',
          confidence: 0,
          publish_time: null,
          sources: [],
          evidences: ['图片中无可识别文本'],
          analysis_time: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      }
    }

    // 初始化AI SDK
    const auth = app.auth({
      storage: sessionStorage,
      captchaOptions: {
        openURIWithCallback: () => console.log("open uri with callback"),
      },
    })

    await auth.signInAnonymously() // 匿名登录
    const ai = await app.ai()

    // 使用OCR识别出的文本进行信息核查
    const model = ai.createModel("deepseek")

    const systemPrompt = `你是一个专业的信息核查助手，负责分析从图片中提取的文本信息的真实性。请按照以下要求进行分析：

1. 仔细分析从图片OCR提取的文本内容
2. 识别关键信息和事实声明
3. 基于你的知识库判断信息的可信度
4. 考虑图片文本可能存在的OCR识别错误
5. 提供简洁明确的核查结果

请严格按照以下JSON格式返回结果：
{
  "status": "supported|disputed|insufficient",
  "summary": "核查结果的简要说明",
  "confidence": 0.0-1.0之间的数值,
  "analysis": "详细分析过程",
  "key_points": ["关键信息点1", "关键信息点2"],
  "recommendations": ["建议1", "建议2"]
}

状态说明：
- supported: 信息可信，有证据支持
- disputed: 信息存疑，有证据反驳或矛盾
- insufficient: 信息不足，无法做出明确判断`

    // 调用AI模型进行文本分析
    const aiResponse = await model.streamText({
      model: "deepseek-r1",
      messages: [
        { role: "system", content: systemPrompt },
        {
          role: "user",
          content: `请核查以下从图片中提取的文本信息的真实性：\n\nOCR识别置信度: ${mockOCRResult.confidence}\n\n提取的文本内容：\n${mockOCRResult.extracted_text}`
        }
      ],
      temperature: 0.3,
      max_tokens: 1500
    })

    // 收集AI响应
    let aiResult = ''
    for await (let chunk of aiResponse.textStream) {
      aiResult += chunk
    }

    console.log('AI分析结果:', aiResult)

    // 解析AI返回的JSON结果
    let analysisResult
    try {
      const jsonMatch = aiResult.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('无法解析AI返回的JSON格式')
      }
    } catch (parseError) {
      console.error('解析AI结果失败:', parseError)
      analysisResult = {
        status: 'insufficient',
        summary: '分析过程中遇到技术问题，建议人工核查',
        confidence: 0.5,
        analysis: aiResult,
        key_points: ['技术分析异常'],
        recommendations: ['建议通过其他渠道验证信息真实性']
      }
    }

    // 模拟生成一些示例来源
    const mockSources = [
      {
        title: '相关图片信息报道',
        url: 'https://example.com/image-news/1',
        credibility: 0.8,
        publish_date: '2024-01-15'
      }
    ]

    // 构建返回结果
    const result = {
      check_id: checkId,
      ocr_result: mockOCRResult,
      status: analysisResult.status,
      summary: analysisResult.summary,
      confidence: analysisResult.confidence * mockOCRResult.confidence, // 综合OCR和分析的置信度
      publish_time: null,
      sources: analysisResult.status !== 'insufficient' ? mockSources : [],
      evidences: analysisResult.key_points || [],
      analysis_time: new Date().toISOString(),
      detailed_analysis: analysisResult.analysis,
      recommendations: analysisResult.recommendations || []
    }

    console.log('图片核查完成，结果:', result)

    return {
      code: 200,
      message: 'success',
      data: result,
      timestamp: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('图片核查失败:', error)
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}
