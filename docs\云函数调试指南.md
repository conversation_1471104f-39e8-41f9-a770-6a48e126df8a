# 云函数调试指南

## 当前问题分析

根据错误信息分析：
1. **API弃用警告**: `wx.getSystemInfoSync`已被弃用，但这不影响功能
2. **云函数调用失败**: 返回"服务器内部错误"，可能的原因：
   - 云函数未正确部署
   - 依赖模块未安装
   - 环境配置问题
   - AI模型权限问题

## 调试步骤

### 步骤1: 使用调试工具页面

我已经创建了一个专门的调试页面，可以帮助诊断问题：

1. 在首页点击"云函数调试工具"按钮
2. 或直接访问 `/pages/debug/debug` 页面
3. 按顺序测试各个功能

### 步骤2: 检查云函数部署状态

#### 方法一: 微信开发者工具检查
1. 打开微信开发者工具
2. 点击"云开发"按钮
3. 查看"云函数"列表
4. 确认以下云函数是否存在：
   - `add` (测试云函数)
   - `checkText` (文本核查)
   - `checkImage` (图片核查)
   - `submitFeedback` (用户反馈)
   - `getHistory` (历史记录)

#### 方法二: 调试页面检查
使用调试页面的"查询云函数列表"功能自动检查。

### 步骤3: 重新部署云函数

如果云函数不存在或有问题，请按以下步骤重新部署：

```bash
# 1. 更新云函数配置
npm run update:cloud-configs

# 2. 安装云函数依赖
npm run install:cloud-deps

# 3. 构建项目
npm run build:mp-weixin
```

然后在微信开发者工具中：
1. 右键点击每个云函数目录
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 步骤4: 检查AI能力配置

checkText和checkImage云函数需要AI能力支持：

1. 登录[腾讯云开发控制台](https://tcb.cloud.tencent.com/)
2. 选择对应的环境 (cloud1-4gqrn7kua46d9633)
3. 在左侧菜单找到"AI+模块"
4. 开通DeepSeek模型访问权限
5. 确保小程序基础库版本 >= 3.7.1

### 步骤5: 验证环境配置

检查以下配置是否正确：

#### manifest.json
```json
{
  "mp-weixin": {
    "appid": "wx042a805fca93fa5d",
    "cloudfunctionRoot": "cloudfunctions/"
  }
}
```

#### cloudbaserc.json
```json
{
  "envId": "cloud1-4gqrn7kua46d9633"
}
```

## 常见问题解决

### Q1: 云函数调用返回404错误
**原因**: 云函数未部署或名称不匹配
**解决**: 重新部署云函数，确保名称正确

### Q2: 云函数调用返回500错误
**原因**: 云函数内部错误，通常是依赖问题
**解决**: 
1. 检查云函数日志
2. 重新安装依赖
3. 检查代码语法错误

### Q3: AI模型调用失败
**原因**: 未开通AI能力或权限不足
**解决**: 
1. 在云开发控制台开通AI能力
2. 确保环境ID正确
3. 检查模型调用权限

### Q4: 依赖模块找不到
**原因**: wx-server-sdk未正确安装
**解决**: 
1. 运行 `npm run install:cloud-deps`
2. 重新部署云函数时选择"云端安装依赖"

## 调试技巧

### 1. 查看云函数日志
在微信开发者工具中：
1. 打开云开发控制台
2. 点击"云函数"
3. 选择具体的云函数
4. 查看"调用日志"

### 2. 本地测试云函数
```javascript
// 在调试页面中测试
const testResult = await wx.cloud.callFunction({
  name: 'add',
  data: { a: 1, b: 2 }
})
console.log('测试结果:', testResult)
```

### 3. 分步骤测试
1. 先测试简单的add云函数
2. 再测试checkText云函数
3. 最后测试完整的核查流程

### 4. 检查网络连接
确保开发环境能够正常访问腾讯云服务。

## 预期的正常结果

### add云函数测试成功
```json
{
  "result": {
    "sum": 3,
    "eventParams": {"a": 1, "b": 2},
    "userInfo": {
      "openid": "用户openid",
      "appid": "小程序appid"
    }
  }
}
```

### checkText云函数测试成功
```json
{
  "result": {
    "code": 200,
    "message": "success",
    "data": {
      "check_id": "check_xxx",
      "status": "supported|disputed|insufficient",
      "summary": "核查结果摘要",
      "confidence": 0.85
    }
  }
}
```

## 紧急修复方案

如果云函数仍然无法正常工作，可以使用模拟数据进行测试：

1. 在首页的handleSubmit函数中添加模拟数据
2. 跳过云函数调用，直接返回测试结果
3. 验证前端逻辑是否正常

```javascript
// 临时模拟数据
const mockResult = {
  check_id: 'mock_' + Date.now(),
  status: 'supported',
  summary: '这是一个模拟的核查结果，用于测试前端功能',
  confidence: 0.8,
  sources: [],
  evidences: ['模拟证据1', '模拟证据2']
}
```

## 联系支持

如果问题仍然无法解决：
1. 收集调试页面的测试结果
2. 导出云函数日志
3. 记录具体的错误信息
4. 联系技术支持团队

## 总结

通过系统性的调试步骤，大多数云函数问题都可以得到解决。关键是：
1. 确保云函数正确部署
2. 验证依赖安装完整
3. 检查AI能力配置
4. 使用调试工具辅助诊断

调试页面提供了完整的测试功能，建议优先使用它来诊断问题。
