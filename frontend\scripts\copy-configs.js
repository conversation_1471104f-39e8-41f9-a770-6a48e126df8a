const fs = require('fs')
const path = require('path')

console.log('📋 开始复制配置文件...')

const buildDir = path.join(__dirname, '../dist/build/mp-weixin')

// 确保构建目录存在
if (!fs.existsSync(buildDir)) {
  console.error('❌ 构建目录不存在:', buildDir)
  console.log('💡 请先运行构建命令: npm run build:mp-weixin')
  process.exit(1)
}

// 需要复制的配置文件列表
const configFiles = [
  {
    src: 'cloudbaserc.json',
    dest: 'cloudbaserc.json',
    required: true,
    description: '云开发配置文件'
  },
  {
    src: 'project.config.json',
    dest: 'project.config.json',
    required: true,
    description: '项目配置文件'
  },
  {
    src: 'package.json',
    dest: 'package.json',
    required: false,
    description: '包配置文件'
  },
  {
    src: '.gitignore',
    dest: '.gitignore',
    required: false,
    description: 'Git忽略文件'
  }
]

let copiedCount = 0
let errorCount = 0

// 复制配置文件
configFiles.forEach(({ src, dest, required, description }) => {
  const sourcePath = path.join(__dirname, '..', src)
  const targetPath = path.join(buildDir, dest)
  
  try {
    if (fs.existsSync(sourcePath)) {
      // 读取源文件内容
      const content = fs.readFileSync(sourcePath, 'utf8')
      
      // 对于某些文件，可能需要特殊处理
      let processedContent = content
      
      if (src === 'cloudbaserc.json') {
        // 验证cloudbaserc.json格式
        try {
          const config = JSON.parse(content)
          if (!config.envId) {
            console.warn(`⚠️ ${description}: 缺少envId配置`)
          }
          processedContent = JSON.stringify(config, null, 2)
        } catch (parseError) {
          console.error(`❌ ${description}: JSON格式错误`, parseError.message)
          errorCount++
          return
        }
      }
      
      if (src === 'project.config.json') {
        // 验证project.config.json格式
        try {
          const config = JSON.parse(content)
          if (!config.appid) {
            console.warn(`⚠️ ${description}: 缺少appid配置`)
          }
          if (!config.cloudfunctionRoot) {
            console.warn(`⚠️ ${description}: 缺少cloudfunctionRoot配置`)
          }
          processedContent = JSON.stringify(config, null, 2)
        } catch (parseError) {
          console.error(`❌ ${description}: JSON格式错误`, parseError.message)
          errorCount++
          return
        }
      }
      
      // 写入目标文件
      fs.writeFileSync(targetPath, processedContent)
      console.log(`✅ ${description} 已复制: ${src} -> ${dest}`)
      copiedCount++
      
    } else {
      if (required) {
        console.error(`❌ ${description} 不存在: ${sourcePath}`)
        errorCount++
      } else {
        console.log(`⚠️ ${description} 不存在（可选）: ${sourcePath}`)
      }
    }
  } catch (error) {
    console.error(`❌ 复制 ${description} 失败:`, error.message)
    errorCount++
  }
})

// 验证复制结果
console.log('\n🔍 验证复制结果...')

const verificationFiles = [
  'cloudbaserc.json',
  'project.config.json'
]

verificationFiles.forEach(file => {
  const filePath = path.join(buildDir, file)
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath)
    console.log(`✅ ${file}: 存在 (${stats.size} bytes)`)
    
    // 验证JSON文件格式
    if (file.endsWith('.json')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8')
        JSON.parse(content)
        console.log(`   📝 ${file}: JSON格式正确`)
      } catch (error) {
        console.error(`   ❌ ${file}: JSON格式错误`, error.message)
        errorCount++
      }
    }
  } else {
    console.error(`❌ ${file}: 不存在`)
    errorCount++
  }
})

// 输出总结
console.log('\n📊 复制总结:')
console.log(`✅ 成功复制: ${copiedCount} 个文件`)
if (errorCount > 0) {
  console.log(`❌ 错误数量: ${errorCount} 个`)
  console.log('\n💡 建议:')
  console.log('1. 检查源文件是否存在')
  console.log('2. 验证JSON文件格式是否正确')
  console.log('3. 确保有足够的文件写入权限')
  process.exit(1)
} else {
  console.log('🎉 所有配置文件复制完成!')
}

// 显示下一步操作提示
console.log('\n💡 下一步操作:')
console.log('1. 在微信开发者工具中打开构建目录')
console.log('2. 检查云开发环境是否正确配置')
console.log('3. 上传并部署云函数')
console.log(`📂 构建目录: ${buildDir}`)
