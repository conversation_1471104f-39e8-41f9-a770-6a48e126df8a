const fs = require('fs')
const path = require('path')

console.log('🔄 更新云函数Node.js版本到18+...')

const cloudfunctionsDir = path.join(__dirname, '../cloudfunctions')

// 检查云函数目录是否存在
if (!fs.existsSync(cloudfunctionsDir)) {
  console.error('❌ 云函数目录不存在:', cloudfunctionsDir)
  process.exit(1)
}

// 获取所有云函数
const cloudFunctions = fs.readdirSync(cloudfunctionsDir).filter(item => {
  const itemPath = path.join(cloudfunctionsDir, item)
  return fs.statSync(itemPath).isDirectory()
})

console.log('📁 发现云函数:', cloudFunctions.join(', '))

// Node.js 18版本配置
const nodeJsConfig = {
  runtime: "Nodejs18.15", // 使用Node.js 18.15 LTS版本
  memorySize: 256, // 默认内存
  timeout: 30, // 默认超时
  installDependency: true
}

// AI云函数需要更多资源
const aiFunctions = ['checkText', 'checkImage']
const aiConfig = {
  runtime: "Nodejs18.15",
  memorySize: 512, // AI功能需要更多内存
  timeout: 60, // AI功能需要更长超时时间
  installDependency: true
}

// 更新每个云函数的配置
cloudFunctions.forEach(funcName => {
  const funcDir = path.join(cloudfunctionsDir, funcName)
  const configPath = path.join(funcDir, 'config.json')
  
  // 确定使用哪个配置
  const isAiFunction = aiFunctions.includes(funcName)
  const targetConfig = isAiFunction ? aiConfig : nodeJsConfig
  
  try {
    let config = {}
    
    // 如果config.json存在，读取现有配置
    if (fs.existsSync(configPath)) {
      const existingConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'))
      config = { ...existingConfig }
      console.log(`📖 ${funcName}: 读取现有配置`)
    } else {
      console.log(`📝 ${funcName}: 创建新配置文件`)
    }
    
    // 更新关键配置
    const oldRuntime = config.runtime
    config.runtime = targetConfig.runtime
    config.memorySize = targetConfig.memorySize
    config.timeout = targetConfig.timeout
    config.installDependency = targetConfig.installDependency
    
    // 确保有基础字段
    if (!config.permissions) {
      config.permissions = { openapi: [] }
    }
    if (!config.triggers) {
      config.triggers = []
    }
    if (!config.envVariables) {
      config.envVariables = {}
    }
    
    // 写入配置文件
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
    
    // 输出更新信息
    if (oldRuntime && oldRuntime !== targetConfig.runtime) {
      console.log(`✅ ${funcName}: ${oldRuntime} → ${targetConfig.runtime}`)
    } else {
      console.log(`✅ ${funcName}: 设置为 ${targetConfig.runtime}`)
    }
    
    if (isAiFunction) {
      console.log(`   🧠 AI功能: 内存${targetConfig.memorySize}MB, 超时${targetConfig.timeout}s`)
    } else {
      console.log(`   ⚙️ 基础功能: 内存${targetConfig.memorySize}MB, 超时${targetConfig.timeout}s`)
    }
    
  } catch (error) {
    console.error(`❌ ${funcName}: 配置更新失败`, error.message)
  }
})

// 更新cloudbaserc.json中的函数配置
const cloudbasercPath = path.join(__dirname, '../cloudbaserc.json')
if (fs.existsSync(cloudbasercPath)) {
  try {
    const cloudbaserc = JSON.parse(fs.readFileSync(cloudbasercPath, 'utf8'))
    
    if (cloudbaserc.functions && Array.isArray(cloudbaserc.functions)) {
      cloudbaserc.functions.forEach(func => {
        const isAiFunction = aiFunctions.includes(func.name)
        const targetConfig = isAiFunction ? aiConfig : nodeJsConfig
        
        // 更新配置（但不添加runtime，因为cloudbaserc.json不需要这个字段）
        func.memorySize = targetConfig.memorySize
        func.timeout = targetConfig.timeout
        func.installDependency = targetConfig.installDependency
      })
      
      fs.writeFileSync(cloudbasercPath, JSON.stringify(cloudbaserc, null, 2))
      console.log('✅ cloudbaserc.json 已同步更新')
    }
  } catch (error) {
    console.warn('⚠️ cloudbaserc.json 更新失败:', error.message)
  }
}

console.log('\n📋 Node.js版本更新总结:')
console.log('🔧 所有云函数已升级到 Node.js 18.15 LTS')
console.log('🧠 AI功能云函数配置:')
aiFunctions.forEach(func => {
  if (cloudFunctions.includes(func)) {
    console.log(`   ✅ ${func}: 512MB内存, 60s超时`)
  }
})

console.log('⚙️ 基础功能云函数配置:')
cloudFunctions.filter(func => !aiFunctions.includes(func)).forEach(func => {
  console.log(`   ✅ ${func}: 256MB内存, 30s超时`)
})

console.log('\n💡 重要提醒:')
console.log('1. Node.js 18+ 是CloudBase AI SDK的必需版本')
console.log('2. 请在微信开发者工具中重新部署所有云函数')
console.log('3. 确保选择"上传并部署：云端安装依赖"')
console.log('4. 部署时会自动使用新的Node.js版本')

console.log('\n📖 Node.js 18 新特性:')
console.log('- 更好的性能和内存管理')
console.log('- 原生支持ES2022特性')
console.log('- 改进的错误处理')
console.log('- 更好的CloudBase AI SDK兼容性')

console.log('\n🎉 Node.js版本更新完成!')
