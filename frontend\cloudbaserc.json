{"envId": "cloud1-4gqrn7kua46d9633", "framework": {"name": "uni-app", "plugins": {"client": {"use": "@cloudbase/framework-plugin-mp", "inputs": {"buildCommand": "npm run build:mp-weixin", "outputPath": "dist/build/mp-weixin"}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "add", "config": {"timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 256, "installDependency": true}}, {"name": "checkText", "config": {"timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}}, {"name": "checkImage", "config": {"timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}}, {"name": "submitFeedback", "config": {"timeout": 30, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 256, "installDependency": true}}, {"name": "getHistory", "config": {"timeout": 30, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 256, "installDependency": true}}]}}}}, "region": "ap-shanghai", "$schema": "https://framework-1258016615.tcloudbaseapp.com/schema/latest.json"}