{"envId": "cloud1-4gqrn7kua46d9633", "functions": [{"name": "add", "timeout": 30, "memorySize": 256, "installDependency": true}, {"name": "checkText", "timeout": 60, "memorySize": 512, "installDependency": true}, {"name": "checkImage", "timeout": 60, "memorySize": 512, "installDependency": true}, {"name": "submitFeedback", "timeout": 30, "memorySize": 256, "installDependency": true}, {"name": "getHistory", "timeout": 30, "memorySize": 256, "installDependency": true}]}