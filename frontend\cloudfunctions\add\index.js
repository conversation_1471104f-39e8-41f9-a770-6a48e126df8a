// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数add被调用，事件参数:', event)
  console.log('上下文信息:', context)
  
  try {
    const wxContext = cloud.getWXContext()
    console.log('用户信息:', {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    })

    const result = {
      sum: event.a + event.b,
      eventParams: event,
      userInfo: {
        openid: wxContext.OPENID,
        appid: wxContext.APPID
      },
      envInfo: {
        envId: cloud.DYNAMIC_CURRENT_ENV,
        callTime: new Date().toISOString()
      }
    }
    
    console.log('计算结果:', result)
    return result
    
  } catch (err) {
    console.error('云函数执行错误:', err)
    return {
      error: err.message,
      stack: err.stack,
      env: cloud.DYNAMIC_CURRENT_ENV
    }
  }
}
