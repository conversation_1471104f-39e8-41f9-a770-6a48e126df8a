# 云函数部署指南

## 概述

本项目已经实现了基于腾讯云开发的完整后端API，通过云函数的方式提供服务。本文档将指导您如何部署和配置这些云函数。

## 云函数列表

项目包含以下4个云函数：

1. **checkText** - 文本内容核查
2. **checkImage** - 图片内容核查  
3. **submitFeedback** - 用户反馈提交
4. **getHistory** - 历史记录查询
5. **add** - 测试云函数（已存在）

## 前置条件

### 1. 开通云开发环境
- 在微信开发者工具中打开项目
- 点击工具栏的"云开发"按钮
- 创建云开发环境（首次使用免费）
- 记录环境ID（格式如：cloud1-4gqrn7kua46d9633）

### 2. 开通AI能力
- 进入[腾讯云开发控制台](https://tcb.cloud.tencent.com/)
- 在AI+模块中开通DeepSeek模型访问权限
- 确保小程序基础库版本 >= 3.7.1

### 3. 配置数据库
在云开发控制台创建以下数据库集合：
- `user_feedback` - 存储用户反馈数据
- `check_history` - 存储核查历史记录（可选）

## 部署步骤

### 1. 更新环境ID
在 `frontend/src/pages/index/index.vue` 中更新云开发环境ID：

```javascript
wx.cloud.init({
  env: "your-cloud-env-id", // 替换为您的环境ID
  traceUser: true
})
```

### 2. 部署云函数

#### 方法一：通过微信开发者工具部署
1. 在微信开发者工具中打开项目
2. 右键点击 `cloudfunctions` 目录
3. 选择"同步云函数列表"
4. 分别右键点击每个云函数目录，选择"上传并部署"

#### 方法二：通过命令行部署
```bash
# 安装云开发CLI工具
npm install -g @cloudbase/cli

# 登录
tcb login

# 部署所有云函数
tcb functions:deploy checkText
tcb functions:deploy checkImage  
tcb functions:deploy submitFeedback
tcb functions:deploy getHistory
```

### 3. 配置云函数权限
在云开发控制台为每个云函数配置以下权限：
- 数据库读写权限
- 云存储读写权限
- AI模型调用权限

### 4. 测试部署
在微信开发者工具中测试云函数：
1. 打开调试器
2. 点击"测试云函数"按钮
3. 测试add云函数是否正常工作

## 功能说明

### 1. checkText 云函数
- **功能**: 接收文本内容，使用DeepSeek模型进行信息核查
- **输入参数**:
  ```javascript
  {
    text: "待核查的文本内容",
    user_id: "用户ID（可选）",
    session_id: "会话ID（可选）"
  }
  ```
- **返回结果**: 包含核查状态、摘要、置信度、证据等信息

### 2. checkImage 云函数
- **功能**: 接收图片文件ID，进行OCR识别后核查文本内容
- **输入参数**:
  ```javascript
  {
    fileID: "云存储文件ID",
    user_id: "用户ID（可选）",
    session_id: "会话ID（可选）"
  }
  ```
- **返回结果**: 包含OCR结果和核查结果

### 3. submitFeedback 云函数
- **功能**: 接收用户对核查结果的反馈评价
- **输入参数**:
  ```javascript
  {
    check_id: "核查任务ID",
    rating: "accurate|inaccurate",
    comment: "用户评论（可选）",
    user_id: "用户ID（可选）"
  }
  ```
- **返回结果**: 反馈提交确认信息

### 4. getHistory 云函数
- **功能**: 查询用户的云端历史记录
- **输入参数**:
  ```javascript
  {
    user_id: "用户ID（可选）",
    page: 1,
    limit: 20
  }
  ```
- **返回结果**: 分页的历史记录列表

## 前端集成

### 1. 调用云函数示例

```javascript
// 文本核查
const textResult = await wx.cloud.callFunction({
  name: 'checkText',
  data: {
    text: '待核查的文本内容',
    user_id: 'user_123'
  }
})

// 图片核查
const imageResult = await wx.cloud.callFunction({
  name: 'checkImage', 
  data: {
    fileID: 'cloud://xxx.jpg',
    user_id: 'user_123'
  }
})

// 提交反馈
const feedbackResult = await wx.cloud.callFunction({
  name: 'submitFeedback',
  data: {
    check_id: 'check_123',
    rating: 'accurate'
  }
})
```

### 2. 错误处理
```javascript
try {
  const result = await wx.cloud.callFunction({
    name: 'checkText',
    data: { text: 'test' }
  })
  
  if (result.result.code === 200) {
    // 处理成功结果
    console.log(result.result.data)
  } else {
    // 处理业务错误
    console.error(result.result.message)
  }
} catch (error) {
  // 处理系统错误
  console.error('云函数调用失败:', error)
}
```

## 注意事项

### 1. AI模型调用限制
- DeepSeek模型有调用频率限制
- 建议实施客户端请求频率控制
- 监控模型调用量和费用

### 2. 数据存储
- 用户反馈数据会存储在云数据库中
- 图片文件会临时存储在云存储中
- 注意数据隐私保护和清理策略

### 3. 性能优化
- 云函数有冷启动时间，首次调用可能较慢
- 可以考虑使用云函数预热功能
- 对于高频调用，建议升级云函数配置

### 4. 成本控制
- 监控云函数调用次数和执行时间
- 监控AI模型token消耗
- 设置合理的资源配额和告警

## 故障排查

### 1. 云函数调用失败
- 检查环境ID是否正确
- 检查云函数是否部署成功
- 查看云函数日志

### 2. AI模型调用失败
- 检查是否开通AI能力
- 检查模型调用权限
- 查看具体错误信息

### 3. 数据库操作失败
- 检查数据库集合是否创建
- 检查云函数数据库权限
- 查看数据库操作日志

## 扩展功能

### 1. 接入真实OCR服务
当前图片核查使用模拟OCR结果，可以接入腾讯云OCR API：

```javascript
// 在checkImage云函数中添加
const ocr = require('tencentcloud-sdk-nodejs').ocr
const OcrClient = ocr.v20181119.Client

const client = new OcrClient({
  credential: {
    secretId: "your-secret-id",
    secretKey: "your-secret-key"
  },
  region: "ap-beijing"
})
```

### 2. 接入网络搜索
可以接入搜索引擎API来获取实时信息：

```javascript
// 添加网络搜索功能
const searchResult = await searchAPI.search(query)
```

### 3. 增强AI分析
可以使用更复杂的提示词和多轮对话来提高分析准确性。

## 监控和维护

### 1. 设置监控告警
- 云函数执行失败告警
- AI模型调用异常告警
- 数据库操作异常告警

### 2. 定期维护
- 清理过期的临时文件
- 分析用户反馈数据
- 优化AI提示词和模型参数

### 3. 版本管理
- 使用云函数版本管理功能
- 保留历史版本以便回滚
- 记录每次更新的变更内容
